/**
 * PulsePress Accessibility Features
 * 
 * Handles accessibility improvements and user preferences
 */

(function($) {
    'use strict';

    // Initialize accessibility features
    $(document).ready(function() {
        initAccessibilityToolbar();
        initKeyboardNavigation();
        initFocusManagement();
        initScreenReaderSupport();
        loadUserPreferences();
    });

    /**
     * Initialize accessibility toolbar
     */
    function initAccessibilityToolbar() {
        const toolbar = $('.accessibility-toolbar');
        if (toolbar.length === 0) return;

        // Font size controls
        $('#font-size-decrease').on('click', function() {
            adjustFontSize('decrease');
        });

        $('#font-size-increase').on('click', function() {
            adjustFontSize('increase');
        });

        // High contrast toggle
        $('#high-contrast-toggle').on('click', function() {
            toggleHighContrast();
        });

        // Dyslexia-friendly font toggle
        $('#dyslexia-font-toggle').on('click', function() {
            toggleDyslexiaFont();
        });

        // Focus mode toggle
        $('#focus-mode-toggle').on('click', function() {
            toggleFocusMode();
        });

        // Keyboard support for toolbar
        toolbar.on('keydown', function(e) {
            const buttons = toolbar.find('button');
            const currentIndex = buttons.index($(e.target));
            
            switch(e.key) {
                case 'ArrowDown':
                case 'ArrowRight':
                    e.preventDefault();
                    const nextIndex = (currentIndex + 1) % buttons.length;
                    buttons.eq(nextIndex).focus();
                    break;
                case 'ArrowUp':
                case 'ArrowLeft':
                    e.preventDefault();
                    const prevIndex = (currentIndex - 1 + buttons.length) % buttons.length;
                    buttons.eq(prevIndex).focus();
                    break;
                case 'Home':
                    e.preventDefault();
                    buttons.first().focus();
                    break;
                case 'End':
                    e.preventDefault();
                    buttons.last().focus();
                    break;
            }
        });
    }

    /**
     * Adjust font size
     */
    function adjustFontSize(direction) {
        const body = $('body');
        const currentSize = body.attr('class').match(/font-size-(\w+)/);
        
        // Remove current font size class
        body.removeClass('font-size-small font-size-large font-size-xl');
        
        if (direction === 'increase') {
            if (!currentSize || currentSize[1] === 'normal') {
                body.addClass('font-size-large');
                saveUserPreference('fontSize', 'large');
            } else if (currentSize[1] === 'large') {
                body.addClass('font-size-xl');
                saveUserPreference('fontSize', 'xl');
            }
        } else if (direction === 'decrease') {
            if (!currentSize || currentSize[1] === 'normal') {
                body.addClass('font-size-small');
                saveUserPreference('fontSize', 'small');
            } else if (currentSize[1] === 'large') {
                // Back to normal (no class)
                saveUserPreference('fontSize', 'normal');
            } else if (currentSize[1] === 'xl') {
                body.addClass('font-size-large');
                saveUserPreference('fontSize', 'large');
            }
        }
        
        announceToScreenReader('Font size changed');
    }

    /**
     * Toggle high contrast mode
     */
    function toggleHighContrast() {
        const body = $('body');
        const button = $('#high-contrast-toggle');
        
        if (body.hasClass('high-contrast')) {
            body.removeClass('high-contrast');
            button.removeClass('active').attr('aria-pressed', 'false');
            saveUserPreference('highContrast', false);
            announceToScreenReader('High contrast disabled');
        } else {
            body.addClass('high-contrast');
            button.addClass('active').attr('aria-pressed', 'true');
            saveUserPreference('highContrast', true);
            announceToScreenReader('High contrast enabled');
        }
    }

    /**
     * Toggle dyslexia-friendly font
     */
    function toggleDyslexiaFont() {
        const body = $('body');
        const button = $('#dyslexia-font-toggle');
        
        if (body.hasClass('dyslexia-font')) {
            body.removeClass('dyslexia-font');
            button.removeClass('active').attr('aria-pressed', 'false');
            saveUserPreference('dyslexiaFont', false);
            announceToScreenReader('Dyslexia-friendly font disabled');
        } else {
            body.addClass('dyslexia-font');
            button.addClass('active').attr('aria-pressed', 'true');
            saveUserPreference('dyslexiaFont', true);
            announceToScreenReader('Dyslexia-friendly font enabled');
        }
    }

    /**
     * Toggle focus mode
     */
    function toggleFocusMode() {
        const body = $('body');
        const button = $('#focus-mode-toggle');
        
        if (body.hasClass('focus-mode')) {
            body.removeClass('focus-mode');
            button.removeClass('active').attr('aria-pressed', 'false');
            saveUserPreference('focusMode', false);
            announceToScreenReader('Focus mode disabled');
        } else {
            body.addClass('focus-mode');
            button.addClass('active').attr('aria-pressed', 'true');
            saveUserPreference('focusMode', true);
            announceToScreenReader('Focus mode enabled');
        }
    }

    /**
     * Initialize keyboard navigation
     */
    function initKeyboardNavigation() {
        // Escape key to close modals/dropdowns
        $(document).on('keydown', function(e) {
            if (e.key === 'Escape') {
                // Close mobile menu
                if ($('.mobile-menu').hasClass('active')) {
                    $('.menu-toggle').click();
                }
                
                // Close search overlay
                if ($('.search-overlay').hasClass('active')) {
                    $('.search-close').click();
                }
                
                // Close any open dropdowns
                $('.dropdown.open').removeClass('open');
            }
        });

        // Arrow key navigation for menus
        $('.main-menu').on('keydown', 'a', function(e) {
            const menuItems = $('.main-menu a');
            const currentIndex = menuItems.index(this);
            
            switch(e.key) {
                case 'ArrowRight':
                    e.preventDefault();
                    const nextIndex = (currentIndex + 1) % menuItems.length;
                    menuItems.eq(nextIndex).focus();
                    break;
                case 'ArrowLeft':
                    e.preventDefault();
                    const prevIndex = (currentIndex - 1 + menuItems.length) % menuItems.length;
                    menuItems.eq(prevIndex).focus();
                    break;
                case 'ArrowDown':
                    e.preventDefault();
                    // Open submenu if available
                    const submenu = $(this).siblings('.sub-menu');
                    if (submenu.length) {
                        submenu.find('a').first().focus();
                    }
                    break;
                case 'ArrowUp':
                    e.preventDefault();
                    // Go to parent menu item
                    const parentItem = $(this).closest('.sub-menu').siblings('a');
                    if (parentItem.length) {
                        parentItem.focus();
                    }
                    break;
            }
        });

        // Tab trapping for modals
        $(document).on('keydown', '.modal, .overlay', function(e) {
            if (e.key === 'Tab') {
                trapFocus(e, this);
            }
        });
    }

    /**
     * Trap focus within an element
     */
    function trapFocus(e, container) {
        const focusableElements = $(container).find('a, button, input, textarea, select, [tabindex]:not([tabindex="-1"])');
        const firstElement = focusableElements.first();
        const lastElement = focusableElements.last();
        
        if (e.shiftKey) {
            if (document.activeElement === firstElement[0]) {
                e.preventDefault();
                lastElement.focus();
            }
        } else {
            if (document.activeElement === lastElement[0]) {
                e.preventDefault();
                firstElement.focus();
            }
        }
    }

    /**
     * Initialize focus management
     */
    function initFocusManagement() {
        // Store focus before opening modals
        let lastFocusedElement = null;
        
        $(document).on('click', '[data-toggle="modal"]', function() {
            lastFocusedElement = this;
        });
        
        // Restore focus when closing modals
        $(document).on('click', '.modal-close, .overlay-close', function() {
            if (lastFocusedElement) {
                $(lastFocusedElement).focus();
                lastFocusedElement = null;
            }
        });

        // Focus management for dropdown menus
        $('.menu-item-has-children > a').on('click', function(e) {
            e.preventDefault();
            const submenu = $(this).siblings('.sub-menu');
            const isExpanded = $(this).attr('aria-expanded') === 'true';
            
            if (isExpanded) {
                $(this).attr('aria-expanded', 'false');
                submenu.slideUp(200);
                announceToScreenReader(pulsepressA11y.strings.submenu_collapsed);
            } else {
                $(this).attr('aria-expanded', 'true');
                submenu.slideDown(200);
                announceToScreenReader(pulsepressA11y.strings.submenu_expanded);
                // Focus first submenu item
                setTimeout(() => {
                    submenu.find('a').first().focus();
                }, 250);
            }
        });
    }

    /**
     * Initialize screen reader support
     */
    function initScreenReaderSupport() {
        // Create live region for announcements
        if ($('#a11y-announcements').length === 0) {
            $('body').append('<div id="a11y-announcements" aria-live="polite" aria-atomic="true" class="screen-reader-text"></div>');
        }

        // Announce page changes for AJAX content
        $(document).on('ajaxComplete', function() {
            announceToScreenReader('Content updated');
        });

        // Announce form validation errors
        $(document).on('invalid', 'input, textarea, select', function() {
            const errorMessage = this.validationMessage;
            announceToScreenReader('Error: ' + errorMessage);
        });

        // Add ARIA labels to interactive elements without them
        $('button, a, input[type="submit"], input[type="button"]').each(function() {
            if (!$(this).attr('aria-label') && !$(this).attr('aria-labelledby')) {
                const text = $(this).text().trim() || $(this).val() || $(this).attr('title');
                if (text) {
                    $(this).attr('aria-label', text);
                }
            }
        });
    }

    /**
     * Announce message to screen readers
     */
    function announceToScreenReader(message) {
        const announcements = $('#a11y-announcements');
        announcements.text(message);
        
        // Clear after a delay to allow for repeated announcements
        setTimeout(() => {
            announcements.empty();
        }, 1000);
    }

    /**
     * Save user preference
     */
    function saveUserPreference(key, value) {
        if (typeof(Storage) !== "undefined") {
            localStorage.setItem('pulsepress_a11y_' + key, JSON.stringify(value));
        }
    }

    /**
     * Load user preferences
     */
    function loadUserPreferences() {
        if (typeof(Storage) === "undefined") return;
        
        // Load font size preference
        const fontSize = JSON.parse(localStorage.getItem('pulsepress_a11y_fontSize') || 'null');
        if (fontSize && fontSize !== 'normal') {
            $('body').addClass('font-size-' + fontSize);
        }
        
        // Load high contrast preference
        const highContrast = JSON.parse(localStorage.getItem('pulsepress_a11y_highContrast') || 'false');
        if (highContrast) {
            $('body').addClass('high-contrast');
            $('#high-contrast-toggle').addClass('active').attr('aria-pressed', 'true');
        }
        
        // Load dyslexia font preference
        const dyslexiaFont = JSON.parse(localStorage.getItem('pulsepress_a11y_dyslexiaFont') || 'false');
        if (dyslexiaFont) {
            $('body').addClass('dyslexia-font');
            $('#dyslexia-font-toggle').addClass('active').attr('aria-pressed', 'true');
        }
        
        // Load focus mode preference
        const focusMode = JSON.parse(localStorage.getItem('pulsepress_a11y_focusMode') || 'false');
        if (focusMode) {
            $('body').addClass('focus-mode');
            $('#focus-mode-toggle').addClass('active').attr('aria-pressed', 'true');
        }
    }

    /**
     * Handle reduced motion preference
     */
    function handleReducedMotion() {
        if (window.matchMedia('(prefers-reduced-motion: reduce)').matches) {
            $('body').addClass('reduced-motion');
            
            // Disable auto-playing content
            $('video[autoplay]').each(function() {
                this.pause();
                $(this).removeAttr('autoplay');
            });
            
            // Disable auto-scrolling carousels
            $('.carousel').each(function() {
                $(this).carousel('pause');
            });
        }
    }

    // Initialize reduced motion handling
    $(document).ready(handleReducedMotion);

    /**
     * Add focus indicators for mouse users
     */
    function addFocusIndicators() {
        let isMouseUser = false;
        
        $(document).on('mousedown', function() {
            isMouseUser = true;
            $('body').addClass('mouse-user');
        });
        
        $(document).on('keydown', function(e) {
            if (e.key === 'Tab') {
                isMouseUser = false;
                $('body').removeClass('mouse-user');
            }
        });
    }

    // Initialize focus indicators
    $(document).ready(addFocusIndicators);

})(jQuery);
