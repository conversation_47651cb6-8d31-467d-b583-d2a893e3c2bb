<?php
/**
 * SEO Enhancement Features
 *
 * @package PulsePress
 */

// Prevent direct access
if ( ! defined( 'ABSPATH' ) ) {
    exit;
}

/**
 * Initialize SEO features
 */
function pulsepress_init_seo() {
    add_action( 'wp_head', 'pulsepress_add_structured_data' );
    add_action( 'wp_head', 'pulsepress_add_open_graph_tags' );
    add_action( 'wp_head', 'pulsepress_add_twitter_cards' );
    add_filter( 'wp_title', 'pulsepress_optimize_title', 10, 2 );
    add_action( 'wp_head', 'pulsepress_add_meta_description' );
    add_action( 'wp_head', 'pulsepress_add_canonical_url' );
    add_filter( 'the_content', 'pulsepress_add_breadcrumbs' );
    add_action( 'wp_head', 'pulsepress_add_hreflang_tags' );
}
add_action( 'init', 'pulsepress_init_seo' );

/**
 * Add JSON-LD structured data
 */
function pulsepress_add_structured_data() {
    $schema = array();
    
    // Website schema
    $schema['website'] = array(
        '@context' => 'https://schema.org',
        '@type'    => 'WebSite',
        'name'     => get_bloginfo( 'name' ),
        'url'      => home_url(),
        'description' => get_bloginfo( 'description' ),
        'potentialAction' => array(
            '@type'       => 'SearchAction',
            'target'      => home_url( '/?s={search_term_string}' ),
            'query-input' => 'required name=search_term_string',
        ),
    );
    
    // Organization schema
    $logo_id = get_theme_mod( 'custom_logo' );
    if ( $logo_id ) {
        $logo_url = wp_get_attachment_image_url( $logo_id, 'full' );
        $schema['organization'] = array(
            '@context' => 'https://schema.org',
            '@type'    => 'Organization',
            'name'     => get_bloginfo( 'name' ),
            'url'      => home_url(),
            'logo'     => array(
                '@type' => 'ImageObject',
                'url'   => $logo_url,
            ),
            'sameAs'   => pulsepress_get_social_media_urls(),
        );
    }
    
    // Article schema for single posts
    if ( is_single() && get_post_type() === 'post' ) {
        global $post;

        $author = get_userdata( $post->post_author );
        $categories = get_the_category();
        $tags = get_the_tags();

        $article_schema = array(
            '@context'      => 'https://schema.org',
            '@type'         => 'Article',
            'headline'      => get_the_title(),
            'description'   => pulsepress_get_meta_description(),
            'url'           => get_permalink(),
            'datePublished' => get_the_date( 'c' ),
            'dateModified'  => get_the_modified_date( 'c' ),
        );

        // Add author information if valid
        if ( $author && ! is_wp_error( $author ) ) {
            $article_schema['author'] = array(
                '@type' => 'Person',
                'name'  => $author->display_name,
                'url'   => get_author_posts_url( $author->ID ),
            );
        }
            'publisher'     => array(
                '@type' => 'Organization',
                'name'  => get_bloginfo( 'name' ),
                'url'   => home_url(),
            ),
        );
        
        // Add featured image
        if ( has_post_thumbnail() ) {
            $image_id = get_post_thumbnail_id();
            $image_url = wp_get_attachment_image_url( $image_id, 'full' );
            $image_meta = wp_get_attachment_metadata( $image_id );
            
            $article_schema['image'] = array(
                '@type'  => 'ImageObject',
                'url'    => $image_url,
                'width'  => $image_meta['width'] ?? 1200,
                'height' => $image_meta['height'] ?? 630,
            );
        }
        
        // Add categories
        if ( $categories ) {
            $article_schema['articleSection'] = array();
            foreach ( $categories as $category ) {
                $article_schema['articleSection'][] = $category->name;
            }
        }
        
        // Add tags as keywords
        if ( $tags ) {
            $keywords = array();
            foreach ( $tags as $tag ) {
                $keywords[] = $tag->name;
            }
            $article_schema['keywords'] = implode( ', ', $keywords );
        }
        
        // Add reading time
        $reading_time = get_post_meta( get_the_ID(), '_pulsepress_reading_time', true );
        if ( $reading_time ) {
            $article_schema['timeRequired'] = 'PT' . $reading_time . 'M';
        }
        
        $schema['article'] = $article_schema;
    }
    
    // Breadcrumb schema
    if ( ! is_front_page() ) {
        $schema['breadcrumb'] = pulsepress_get_breadcrumb_schema();
    }
    
    // Output schemas
    foreach ( $schema as $type => $data ) {
        echo '<script type="application/ld+json">' . wp_json_encode( $data, JSON_UNESCAPED_SLASHES ) . '</script>' . "\n";
    }
}

/**
 * Add Open Graph tags
 */
function pulsepress_add_open_graph_tags() {
    echo '<meta property="og:site_name" content="' . esc_attr( get_bloginfo( 'name' ) ) . '">' . "\n";
    echo '<meta property="og:locale" content="' . esc_attr( get_locale() ) . '">' . "\n";
    
    if ( is_single() || is_page() ) {
        echo '<meta property="og:type" content="article">' . "\n";
        echo '<meta property="og:title" content="' . esc_attr( get_the_title() ) . '">' . "\n";
        echo '<meta property="og:description" content="' . esc_attr( pulsepress_get_meta_description() ) . '">' . "\n";
        echo '<meta property="og:url" content="' . esc_url( get_permalink() ) . '">' . "\n";
        
        if ( has_post_thumbnail() ) {
            $image_url = wp_get_attachment_image_url( get_post_thumbnail_id(), 'pulsepress-featured' );
            echo '<meta property="og:image" content="' . esc_url( $image_url ) . '">' . "\n";
            echo '<meta property="og:image:width" content="1200">' . "\n";
            echo '<meta property="og:image:height" content="628">' . "\n";
        }
        
        // Article specific tags
        if ( is_single() ) {
            echo '<meta property="article:published_time" content="' . esc_attr( get_the_date( 'c' ) ) . '">' . "\n";
            echo '<meta property="article:modified_time" content="' . esc_attr( get_the_modified_date( 'c' ) ) . '">' . "\n";

            $author_id = get_the_author_meta( 'ID' );
            if ( $author_id ) {
                $author = get_userdata( $author_id );
                if ( $author && ! is_wp_error( $author ) ) {
                    echo '<meta property="article:author" content="' . esc_attr( $author->display_name ) . '">' . "\n";
                }
            }
            
            $categories = get_the_category();
            if ( $categories ) {
                foreach ( $categories as $category ) {
                    echo '<meta property="article:section" content="' . esc_attr( $category->name ) . '">' . "\n";
                }
            }
            
            $tags = get_the_tags();
            if ( $tags ) {
                foreach ( $tags as $tag ) {
                    echo '<meta property="article:tag" content="' . esc_attr( $tag->name ) . '">' . "\n";
                }
            }
        }
    } else {
        echo '<meta property="og:type" content="website">' . "\n";
        echo '<meta property="og:title" content="' . esc_attr( get_bloginfo( 'name' ) ) . '">' . "\n";
        echo '<meta property="og:description" content="' . esc_attr( get_bloginfo( 'description' ) ) . '">' . "\n";
        echo '<meta property="og:url" content="' . esc_url( home_url() ) . '">' . "\n";
    }
}

/**
 * Add Twitter Card tags
 */
function pulsepress_add_twitter_cards() {
    echo '<meta name="twitter:card" content="summary_large_image">' . "\n";
    
    $twitter_handle = get_theme_mod( 'pulsepress_twitter_handle', '' );
    if ( $twitter_handle ) {
        echo '<meta name="twitter:site" content="@' . esc_attr( $twitter_handle ) . '">' . "\n";
    }
    
    if ( is_single() || is_page() ) {
        echo '<meta name="twitter:title" content="' . esc_attr( get_the_title() ) . '">' . "\n";
        echo '<meta name="twitter:description" content="' . esc_attr( pulsepress_get_meta_description() ) . '">' . "\n";
        
        if ( has_post_thumbnail() ) {
            $image_url = wp_get_attachment_image_url( get_post_thumbnail_id(), 'pulsepress-featured' );
            echo '<meta name="twitter:image" content="' . esc_url( $image_url ) . '">' . "\n";
        }
        
        $author_id = get_the_author_meta( 'ID' );
        if ( $author_id ) {
            $author = get_userdata( $author_id );
            if ( $author && ! is_wp_error( $author ) ) {
                $author_twitter = get_user_meta( $author->ID, 'twitter', true );
                if ( $author_twitter ) {
                    echo '<meta name="twitter:creator" content="@' . esc_attr( $author_twitter ) . '">' . "\n";
                }
            }
        }
    } else {
        echo '<meta name="twitter:title" content="' . esc_attr( get_bloginfo( 'name' ) ) . '">' . "\n";
        echo '<meta name="twitter:description" content="' . esc_attr( get_bloginfo( 'description' ) ) . '">' . "\n";
    }
}

/**
 * Get meta description
 */
function pulsepress_get_meta_description() {
    if ( is_single() || is_page() ) {
        // Try custom meta description first
        $meta_desc = get_post_meta( get_the_ID(), '_pulsepress_meta_description', true );
        if ( $meta_desc ) {
            return $meta_desc;
        }
        
        // Try auto-generated summary
        $summary = get_post_meta( get_the_ID(), '_pulsepress_auto_summary', true );
        if ( $summary ) {
            return wp_trim_words( $summary, 25 );
        }
        
        // Fallback to excerpt or content
        $excerpt = get_the_excerpt();
        if ( $excerpt ) {
            return wp_trim_words( $excerpt, 25 );
        }
        
        return wp_trim_words( get_the_content(), 25 );
    }
    
    return get_bloginfo( 'description' );
}

/**
 * Add meta description tag
 */
function pulsepress_add_meta_description() {
    $description = pulsepress_get_meta_description();
    if ( $description ) {
        echo '<meta name="description" content="' . esc_attr( $description ) . '">' . "\n";
    }
}

/**
 * Add canonical URL
 */
function pulsepress_add_canonical_url() {
    if ( is_single() || is_page() ) {
        echo '<link rel="canonical" href="' . esc_url( get_permalink() ) . '">' . "\n";
    } elseif ( is_home() || is_front_page() ) {
        echo '<link rel="canonical" href="' . esc_url( home_url() ) . '">' . "\n";
    } elseif ( is_category() || is_tag() || is_tax() ) {
        echo '<link rel="canonical" href="' . esc_url( get_term_link( get_queried_object() ) ) . '">' . "\n";
    } elseif ( is_author() ) {
        echo '<link rel="canonical" href="' . esc_url( get_author_posts_url( get_queried_object_id() ) ) . '">' . "\n";
    }
}

/**
 * Add breadcrumbs to content
 */
function pulsepress_add_breadcrumbs( $content ) {
    if ( ! is_single() || ! get_theme_mod( 'pulsepress_show_breadcrumbs', true ) ) {
        return $content;
    }
    
    $breadcrumbs = pulsepress_get_breadcrumbs_html();
    return $breadcrumbs . $content;
}

/**
 * Get breadcrumbs HTML
 */
function pulsepress_get_breadcrumbs_html() {
    if ( is_front_page() ) {
        return '';
    }
    
    $breadcrumbs = array();
    $breadcrumbs[] = array(
        'title' => esc_html__( 'Home', 'pulsepress' ),
        'url'   => home_url(),
    );
    
    if ( is_single() ) {
        $categories = get_the_category();
        if ( $categories ) {
            $category = $categories[0];
            $breadcrumbs[] = array(
                'title' => $category->name,
                'url'   => get_category_link( $category->term_id ),
            );
        }
        
        $breadcrumbs[] = array(
            'title' => get_the_title(),
            'url'   => '',
        );
    } elseif ( is_category() ) {
        $category = get_queried_object();
        $breadcrumbs[] = array(
            'title' => $category->name,
            'url'   => '',
        );
    } elseif ( is_tag() ) {
        $tag = get_queried_object();
        $breadcrumbs[] = array(
            'title' => $tag->name,
            'url'   => '',
        );
    } elseif ( is_author() ) {
        $author = get_queried_object();
        if ( $author && isset( $author->display_name ) ) {
            $breadcrumbs[] = array(
                'title' => $author->display_name,
                'url'   => '',
            );
        }
    }
    
    $html = '<nav class="breadcrumbs" aria-label="' . esc_attr__( 'Breadcrumb', 'pulsepress' ) . '">';
    $html .= '<ol class="breadcrumb-list">';
    
    foreach ( $breadcrumbs as $index => $crumb ) {
        $html .= '<li class="breadcrumb-item">';
        
        if ( $crumb['url'] ) {
            $html .= '<a href="' . esc_url( $crumb['url'] ) . '">' . esc_html( $crumb['title'] ) . '</a>';
        } else {
            $html .= '<span aria-current="page">' . esc_html( $crumb['title'] ) . '</span>';
        }
        
        if ( $index < count( $breadcrumbs ) - 1 ) {
            $html .= '<span class="breadcrumb-separator">/</span>';
        }
        
        $html .= '</li>';
    }
    
    $html .= '</ol>';
    $html .= '</nav>';
    
    return $html;
}

/**
 * Get breadcrumb schema
 */
function pulsepress_get_breadcrumb_schema() {
    $breadcrumbs = array();
    $position = 1;
    
    $breadcrumbs[] = array(
        '@type'    => 'ListItem',
        'position' => $position++,
        'name'     => get_bloginfo( 'name' ),
        'item'     => home_url(),
    );
    
    if ( is_single() ) {
        $categories = get_the_category();
        if ( $categories ) {
            $category = $categories[0];
            $breadcrumbs[] = array(
                '@type'    => 'ListItem',
                'position' => $position++,
                'name'     => $category->name,
                'item'     => get_category_link( $category->term_id ),
            );
        }
        
        $breadcrumbs[] = array(
            '@type'    => 'ListItem',
            'position' => $position,
            'name'     => get_the_title(),
            'item'     => get_permalink(),
        );
    }
    
    return array(
        '@context'        => 'https://schema.org',
        '@type'           => 'BreadcrumbList',
        'itemListElement' => $breadcrumbs,
    );
}

/**
 * Get social media URLs
 */
function pulsepress_get_social_media_urls() {
    $social_urls = array();
    
    $social_platforms = array( 'facebook', 'twitter', 'instagram', 'linkedin', 'youtube' );
    
    foreach ( $social_platforms as $platform ) {
        $url = get_theme_mod( 'pulsepress_' . $platform . '_url', '' );
        if ( $url ) {
            $social_urls[] = $url;
        }
    }
    
    return $social_urls;
}

/**
 * Add hreflang tags for multilingual sites
 */
function pulsepress_add_hreflang_tags() {
    // This is a placeholder for multilingual support
    // Implement based on your multilingual plugin (WPML, Polylang, etc.)
    $current_lang = get_locale();
    echo '<link rel="alternate" hreflang="' . esc_attr( $current_lang ) . '" href="' . esc_url( get_permalink() ) . '">' . "\n";
}

/**
 * Add meta box for SEO settings
 */
function pulsepress_add_seo_meta_box() {
    add_meta_box(
        'pulsepress-seo-meta',
        esc_html__( 'SEO Settings', 'pulsepress' ),
        'pulsepress_seo_meta_box_callback',
        'post',
        'normal',
        'default'
    );
}
add_action( 'add_meta_boxes', 'pulsepress_add_seo_meta_box' );

/**
 * SEO meta box callback
 */
function pulsepress_seo_meta_box_callback( $post ) {
    wp_nonce_field( 'pulsepress_seo_meta_box', 'pulsepress_seo_meta_box_nonce' );

    $meta_description = get_post_meta( $post->ID, '_pulsepress_meta_description', true );
    $focus_keyword = get_post_meta( $post->ID, '_pulsepress_focus_keyword', true );

    ?>
    <table class="form-table">
        <tr>
            <th><label for="meta_description"><?php esc_html_e( 'Meta Description', 'pulsepress' ); ?></label></th>
            <td>
                <textarea id="meta_description" name="meta_description" rows="3" style="width: 100%;" maxlength="160"><?php echo esc_textarea( $meta_description ); ?></textarea>
                <p class="description"><?php esc_html_e( 'Recommended length: 150-160 characters', 'pulsepress' ); ?></p>
                <p class="character-count">0 characters</p>
            </td>
        </tr>
        <tr>
            <th><label for="focus_keyword"><?php esc_html_e( 'Focus Keyword', 'pulsepress' ); ?></label></th>
            <td>
                <input type="text" id="focus_keyword" name="focus_keyword" value="<?php echo esc_attr( $focus_keyword ); ?>" style="width: 100%;">
                <p class="description"><?php esc_html_e( 'Main keyword for this post', 'pulsepress' ); ?></p>
            </td>
        </tr>
    </table>

    <script>
    jQuery(document).ready(function($) {
        $('#meta_description').on('input', function() {
            var length = $(this).val().length;
            var counter = $(this).siblings('.character-count');
            counter.text(length + ' characters');

            if (length > 160) {
                counter.css('color', 'red');
            } else if (length > 150) {
                counter.css('color', 'orange');
            } else {
                counter.css('color', 'green');
            }
        }).trigger('input');
    });
    </script>
    <?php
}

/**
 * Save SEO meta box data
 */
function pulsepress_save_seo_meta_box( $post_id ) {
    if ( ! isset( $_POST['pulsepress_seo_meta_box_nonce'] ) ) {
        return;
    }

    if ( ! wp_verify_nonce( $_POST['pulsepress_seo_meta_box_nonce'], 'pulsepress_seo_meta_box' ) ) {
        return;
    }

    if ( defined( 'DOING_AUTOSAVE' ) && DOING_AUTOSAVE ) {
        return;
    }

    if ( ! current_user_can( 'edit_post', $post_id ) ) {
        return;
    }

    if ( isset( $_POST['meta_description'] ) ) {
        update_post_meta( $post_id, '_pulsepress_meta_description', sanitize_textarea_field( $_POST['meta_description'] ) );
    }

    if ( isset( $_POST['focus_keyword'] ) ) {
        update_post_meta( $post_id, '_pulsepress_focus_keyword', sanitize_text_field( $_POST['focus_keyword'] ) );
    }
}
add_action( 'save_post', 'pulsepress_save_seo_meta_box' );

/**
 * Add SEO settings to customizer
 */
function pulsepress_seo_customizer( $wp_customize ) {
    // SEO Section
    $wp_customize->add_section(
        'pulsepress_seo',
        array(
            'title'    => esc_html__( 'SEO Settings', 'pulsepress' ),
            'priority' => 190,
        )
    );

    // Show Breadcrumbs
    $wp_customize->add_setting(
        'pulsepress_show_breadcrumbs',
        array(
            'default'           => true,
            'sanitize_callback' => 'pulsepress_sanitize_checkbox',
        )
    );

    $wp_customize->add_control(
        'pulsepress_show_breadcrumbs',
        array(
            'label'   => esc_html__( 'Show Breadcrumbs', 'pulsepress' ),
            'section' => 'pulsepress_seo',
            'type'    => 'checkbox',
        )
    );

    // Twitter Handle
    $wp_customize->add_setting(
        'pulsepress_twitter_handle',
        array(
            'default'           => '',
            'sanitize_callback' => 'sanitize_text_field',
        )
    );

    $wp_customize->add_control(
        'pulsepress_twitter_handle',
        array(
            'label'       => esc_html__( 'Twitter Handle', 'pulsepress' ),
            'description' => esc_html__( 'Enter without @', 'pulsepress' ),
            'section'     => 'pulsepress_seo',
            'type'        => 'text',
        )
    );
}
add_action( 'customize_register', 'pulsepress_seo_customizer' );
