<?php
/**
 * Performance Optimization Features
 *
 * @package PulsePress
 */

// Prevent direct access
if ( ! defined( 'ABSPATH' ) ) {
    exit;
}

/**
 * Initialize performance optimizations
 */
function pulsepress_init_performance() {
    add_action( 'wp_enqueue_scripts', 'pulsepress_optimize_scripts_styles' );
    add_action( 'wp_head', 'pulsepress_add_preload_hints', 1 );
    add_filter( 'script_loader_tag', 'pulsepress_add_async_defer', 10, 3 );
    add_filter( 'style_loader_tag', 'pulsepress_add_preload_styles', 10, 4 );
    add_action( 'wp_footer', 'pulsepress_critical_css_inline' );
    add_filter( 'wp_lazy_loading_enabled', '__return_true' );
    add_filter( 'wp_get_attachment_image_attributes', 'pulsepress_add_lazy_loading', 10, 3 );
    add_action( 'wp_head', 'pulsepress_add_resource_hints', 2 );
    add_filter( 'wp_calculate_image_srcset', 'pulsepress_optimize_srcset', 10, 5 );
    // Temporarily disable minification to test
    // add_action( 'wp_enqueue_scripts', 'pulsepress_minify_inline_scripts', 999 );
    // add_action( 'wp_print_styles', 'pulsepress_minify_inline_styles', 999 );
}
add_action( 'init', 'pulsepress_init_performance' );

/**
 * Optimize script and style loading
 */
function pulsepress_optimize_scripts_styles() {
    // Remove unused WordPress scripts on non-admin pages
    if ( ! is_admin() ) {
        // Remove emoji scripts if not needed
        if ( ! get_theme_mod( 'pulsepress_enable_emojis', false ) ) {
            remove_action( 'wp_head', 'print_emoji_detection_script', 7 );
            remove_action( 'wp_print_styles', 'print_emoji_styles' );
            remove_action( 'admin_print_scripts', 'print_emoji_detection_script' );
            remove_action( 'admin_print_styles', 'print_emoji_styles' );
        }
        
        // Remove block library CSS if not using Gutenberg blocks
        if ( ! get_theme_mod( 'pulsepress_enable_block_css', true ) ) {
            wp_dequeue_style( 'wp-block-library' );
            wp_dequeue_style( 'wp-block-library-theme' );
        }
    }
    
    // Conditionally load scripts
    if ( ! is_singular() || ! comments_open() ) {
        wp_dequeue_script( 'comment-reply' );
    }
    
    // Load Font Awesome only where needed
    if ( ! is_front_page() && ! is_single() ) {
        wp_dequeue_script( 'font-awesome' );
    }
}

/**
 * Add preload hints for critical resources
 */
function pulsepress_add_preload_hints() {
    // Preload critical fonts
    echo '<link rel="preload" href="' . esc_url( get_template_directory_uri() . '/assets/fonts/montserrat-v25-latin-regular.woff2' ) . '" as="font" type="font/woff2" crossorigin>' . "\n";
    echo '<link rel="preload" href="' . esc_url( get_template_directory_uri() . '/assets/fonts/roboto-v30-latin-regular.woff2' ) . '" as="font" type="font/woff2" crossorigin>' . "\n";
    
    // DNS prefetch for external resources
    echo '<link rel="dns-prefetch" href="//fonts.googleapis.com">' . "\n";
    echo '<link rel="dns-prefetch" href="//kit.fontawesome.com">' . "\n";
    
    // Preconnect to critical third-party domains
    echo '<link rel="preconnect" href="https://fonts.gstatic.com" crossorigin>' . "\n";
}

/**
 * Add async/defer attributes to scripts
 */
function pulsepress_add_async_defer( $tag, $handle, $src ) {
    // Scripts to defer
    $defer_scripts = array(
        'font-awesome',
        'pulsepress-main'
    );
    
    // Scripts to load async
    $async_scripts = array(
        'google-analytics',
        'gtag'
    );
    
    if ( in_array( $handle, $defer_scripts ) ) {
        return str_replace( '<script ', '<script defer ', $tag );
    }
    
    if ( in_array( $handle, $async_scripts ) ) {
        return str_replace( '<script ', '<script async ', $tag );
    }
    
    return $tag;
}

/**
 * Add preload for critical CSS
 */
function pulsepress_add_preload_styles( $html, $handle, $href, $media ) {
    // Critical stylesheets to preload
    $preload_styles = array(
        'pulsepress-style',
        'pulsepress-fonts'
    );
    
    if ( in_array( $handle, $preload_styles ) ) {
        $html = '<link rel="preload" href="' . $href . '" as="style" onload="this.onload=null;this.rel=\'stylesheet\'">' . "\n" . $html;
    }
    
    return $html;
}

/**
 * Inline critical CSS
 */
function pulsepress_critical_css_inline() {
    if ( is_front_page() ) {
        $critical_css = get_theme_mod( 'pulsepress_critical_css', '' );
        if ( ! empty( $critical_css ) ) {
            echo '<style id="critical-css">' . wp_strip_all_tags( $critical_css ) . '</style>';
        }
    }
}

/**
 * Enhanced lazy loading for images
 */
function pulsepress_add_lazy_loading( $attr, $attachment, $size ) {
    // Add loading="lazy" to all images except above-the-fold content
    if ( ! is_admin() && ! wp_is_mobile() ) {
        $attr['loading'] = 'lazy';
        $attr['decoding'] = 'async';
    }
    
    return $attr;
}

/**
 * Remove query strings from static resources
 */
function pulsepress_remove_query_strings( $src ) {
    $parts = explode( '?ver', $src );
    return $parts[0];
}
add_filter( 'script_loader_src', 'pulsepress_remove_query_strings', 15, 1 );
add_filter( 'style_loader_src', 'pulsepress_remove_query_strings', 15, 1 );

/**
 * Enable Gzip compression
 */
function pulsepress_enable_gzip() {
    if ( ! headers_sent() && extension_loaded( 'zlib' ) && ! ini_get( 'zlib.output_compression' ) ) {
        if ( ! ob_start( 'ob_gzhandler' ) ) {
            ob_start();
        }
    }
}
add_action( 'init', 'pulsepress_enable_gzip' );

/**
 * Optimize database queries
 */
function pulsepress_optimize_queries() {
    // Remove unnecessary meta queries
    remove_action( 'wp_head', 'wp_generator' );
    remove_action( 'wp_head', 'wlwmanifest_link' );
    remove_action( 'wp_head', 'rsd_link' );
    remove_action( 'wp_head', 'wp_shortlink_wp_head' );
    
    // Remove REST API links if not needed
    if ( ! get_theme_mod( 'pulsepress_enable_rest_api', true ) ) {
        remove_action( 'wp_head', 'rest_output_link_wp_head' );
        remove_action( 'wp_head', 'wp_oembed_add_discovery_links' );
    }
}
add_action( 'init', 'pulsepress_optimize_queries' );

/**
 * Add performance settings to customizer
 */
function pulsepress_performance_customizer( $wp_customize ) {
    // Performance Section
    $wp_customize->add_section(
        'pulsepress_performance',
        array(
            'title'    => esc_html__( 'Performance Settings', 'pulsepress' ),
            'priority' => 160,
        )
    );
    
    // Enable Emojis
    $wp_customize->add_setting(
        'pulsepress_enable_emojis',
        array(
            'default'           => false,
            'sanitize_callback' => 'pulsepress_sanitize_checkbox',
        )
    );
    
    $wp_customize->add_control(
        'pulsepress_enable_emojis',
        array(
            'label'   => esc_html__( 'Enable WordPress Emojis', 'pulsepress' ),
            'section' => 'pulsepress_performance',
            'type'    => 'checkbox',
        )
    );
    
    // Enable Block CSS
    $wp_customize->add_setting(
        'pulsepress_enable_block_css',
        array(
            'default'           => true,
            'sanitize_callback' => 'pulsepress_sanitize_checkbox',
        )
    );
    
    $wp_customize->add_control(
        'pulsepress_enable_block_css',
        array(
            'label'   => esc_html__( 'Enable Gutenberg Block CSS', 'pulsepress' ),
            'section' => 'pulsepress_performance',
            'type'    => 'checkbox',
        )
    );
    
    // Critical CSS
    $wp_customize->add_setting(
        'pulsepress_critical_css',
        array(
            'default'           => '',
            'sanitize_callback' => 'wp_strip_all_tags',
        )
    );
    
    $wp_customize->add_control(
        'pulsepress_critical_css',
        array(
            'label'       => esc_html__( 'Critical CSS', 'pulsepress' ),
            'description' => esc_html__( 'Add critical CSS for above-the-fold content', 'pulsepress' ),
            'section'     => 'pulsepress_performance',
            'type'        => 'textarea',
        )
    );
}
add_action( 'customize_register', 'pulsepress_performance_customizer' );

/**
 * Add performance monitoring
 */
function pulsepress_performance_monitor() {
    if ( current_user_can( 'manage_options' ) && get_theme_mod( 'pulsepress_show_performance_stats', false ) ) {
        $queries = get_num_queries();
        $memory = size_format( memory_get_peak_usage( true ) );
        $time = timer_stop( 0, 3 );

        echo '<!-- Performance Stats: ' . $queries . ' queries, ' . $memory . ' memory, ' . $time . 's -->';
    }
}
add_action( 'wp_footer', 'pulsepress_performance_monitor' );

/**
 * Add advanced resource hints
 */
function pulsepress_add_resource_hints() {
    // Preconnect to external domains
    $external_domains = array(
        'https://fonts.googleapis.com',
        'https://fonts.gstatic.com',
        'https://kit.fontawesome.com',
        'https://cdnjs.cloudflare.com'
    );

    foreach ( $external_domains as $domain ) {
        echo '<link rel="preconnect" href="' . esc_url( $domain ) . '" crossorigin>' . "\n";
    }

    // Prefetch next page if on single post
    if ( is_single() ) {
        $next_post = get_next_post();
        if ( $next_post ) {
            echo '<link rel="prefetch" href="' . esc_url( get_permalink( $next_post->ID ) ) . '">' . "\n";
        }
    }
}

/**
 * Optimize image srcset for better performance
 */
function pulsepress_optimize_srcset( $sources, $size_array, $image_src, $image_meta, $attachment_id ) {
    // Remove unnecessary sizes from srcset to reduce HTML size
    if ( is_array( $sources ) && count( $sources ) > 4 ) {
        // Keep only the most relevant sizes
        $keep_sizes = array();
        $sizes = array_keys( $sources );
        sort( $sizes );

        // Keep smallest, largest, and 2 middle sizes
        $keep_sizes[] = $sizes[0]; // smallest
        $keep_sizes[] = $sizes[ floor( count( $sizes ) / 3 ) ];
        $keep_sizes[] = $sizes[ floor( 2 * count( $sizes ) / 3 ) ];
        $keep_sizes[] = $sizes[ count( $sizes ) - 1 ]; // largest

        $optimized_sources = array();
        foreach ( $sources as $width => $source ) {
            if ( in_array( $width, $keep_sizes ) ) {
                $optimized_sources[ $width ] = $source;
            }
        }

        return $optimized_sources;
    }

    return $sources;
}

/**
 * Minify inline scripts
 */
function pulsepress_minify_inline_scripts() {
    if ( ! is_admin() ) {
        ob_start( 'pulsepress_minify_js_callback' );
    }
}

/**
 * Minify inline styles
 */
function pulsepress_minify_inline_styles() {
    if ( ! is_admin() ) {
        ob_start( 'pulsepress_minify_css_callback' );
    }
}

/**
 * JavaScript minification callback
 */
function pulsepress_minify_js_callback( $buffer ) {
    // Simple JS minification
    $buffer = preg_replace( '/\/\*[\s\S]*?\*\//', '', $buffer ); // Remove comments
    $buffer = preg_replace( '/\s+/', ' ', $buffer ); // Remove extra whitespace
    return $buffer;
}

/**
 * CSS minification callback
 */
function pulsepress_minify_css_callback( $buffer ) {
    // Simple CSS minification
    $buffer = preg_replace( '/\/\*[\s\S]*?\*\//', '', $buffer ); // Remove comments
    $buffer = preg_replace( '/\s+/', ' ', $buffer ); // Remove extra whitespace
    $buffer = preg_replace( '/;\s*}/', '}', $buffer ); // Remove last semicolon
    return $buffer;
}

/**
 * Add WebP support for images
 */
function pulsepress_add_webp_support() {
    add_filter( 'wp_generate_attachment_metadata', 'pulsepress_generate_webp_images', 10, 2 );
    add_filter( 'wp_get_attachment_image_src', 'pulsepress_serve_webp_images', 10, 4 );
}
add_action( 'init', 'pulsepress_add_webp_support' );

/**
 * Generate WebP versions of uploaded images
 */
function pulsepress_generate_webp_images( $metadata, $attachment_id ) {
    if ( ! function_exists( 'imagewebp' ) ) {
        return $metadata;
    }

    $upload_dir = wp_upload_dir();
    $file_path = get_attached_file( $attachment_id );

    if ( $file_path && file_exists( $file_path ) ) {
        $path_info = pathinfo( $file_path );
        $webp_path = $path_info['dirname'] . '/' . $path_info['filename'] . '.webp';

        // Create WebP version
        $image = null;
        switch ( $path_info['extension'] ) {
            case 'jpg':
            case 'jpeg':
                $image = imagecreatefromjpeg( $file_path );
                break;
            case 'png':
                $image = imagecreatefrompng( $file_path );
                break;
        }

        if ( $image ) {
            imagewebp( $image, $webp_path, 80 );
            imagedestroy( $image );
        }
    }

    return $metadata;
}

/**
 * Serve WebP images when supported
 */
function pulsepress_serve_webp_images( $image, $attachment_id, $size, $icon ) {
    if ( ! $image || ! isset( $_SERVER['HTTP_ACCEPT'] ) ) {
        return $image;
    }

    // Check if browser supports WebP
    if ( strpos( $_SERVER['HTTP_ACCEPT'], 'image/webp' ) !== false ) {
        $webp_url = str_replace( array( '.jpg', '.jpeg', '.png' ), '.webp', $image[0] );
        $webp_path = str_replace( wp_upload_dir()['baseurl'], wp_upload_dir()['basedir'], $webp_url );

        if ( file_exists( $webp_path ) ) {
            $image[0] = $webp_url;
        }
    }

    return $image;
}
