<?php
/**
 * PulsePress Theme Customizer
 *
 * @package PulsePress
 */

/**
 * Add postMessage support for site title and description for the Theme Customizer.
 *
 * @param WP_Customize_Manager $wp_customize Theme Customizer object.
 */
function pulsepress_customize_register( $wp_customize ) {
	$wp_customize->get_setting( 'blogname' )->transport         = 'postMessage';
	$wp_customize->get_setting( 'blogdescription' )->transport  = 'postMessage';
	$wp_customize->get_setting( 'header_textcolor' )->transport = 'postMessage';

	if ( isset( $wp_customize->selective_refresh ) ) {
		$wp_customize->selective_refresh->add_partial(
			'blogname',
			array(
				'selector'        => '.site-title a',
				'render_callback' => 'pulsepress_customize_partial_blogname',
			)
		);
		$wp_customize->selective_refresh->add_partial(
			'blogdescription',
			array(
				'selector'        => '.site-description',
				'render_callback' => 'pulsepress_customize_partial_blogdescription',
			)
		);
	}

	// Add Theme Options Panel
	$wp_customize->add_panel(
		'pulsepress_theme_options',
		array(
			'title'       => esc_html__( 'Theme Options', 'pulsepress' ),
			'description' => esc_html__( 'Configure PulsePress theme settings', 'pulsepress' ),
			'priority'    => 130,
		)
	);

	// Add Color Scheme Section
	$wp_customize->add_section(
		'pulsepress_color_scheme',
		array(
			'title'       => esc_html__( 'Color Scheme', 'pulsepress' ),
			'description' => esc_html__( 'Customize the theme colors', 'pulsepress' ),
			'panel'       => 'pulsepress_theme_options',
			'priority'    => 10,
		)
	);

	// Primary Color
	$wp_customize->add_setting(
		'pulsepress_primary_color',
		array(
			'default'           => '#1e73be',
			'sanitize_callback' => 'sanitize_hex_color',
			'transport'         => 'postMessage',
		)
	);

	$wp_customize->add_control(
		new WP_Customize_Color_Control(
			$wp_customize,
			'pulsepress_primary_color',
			array(
				'label'    => esc_html__( 'Primary Color', 'pulsepress' ),
				'section'  => 'pulsepress_color_scheme',
				'settings' => 'pulsepress_primary_color',
			)
		)
	);

	// Secondary Color
	$wp_customize->add_setting(
		'pulsepress_secondary_color',
		array(
			'default'           => '#ff6b6b',
			'sanitize_callback' => 'sanitize_hex_color',
			'transport'         => 'postMessage',
		)
	);

	$wp_customize->add_control(
		new WP_Customize_Color_Control(
			$wp_customize,
			'pulsepress_secondary_color',
			array(
				'label'    => esc_html__( 'Secondary Color', 'pulsepress' ),
				'section'  => 'pulsepress_color_scheme',
				'settings' => 'pulsepress_secondary_color',
			)
		)
	);

	// Dark Mode Toggle
	$wp_customize->add_setting(
		'pulsepress_dark_mode',
		array(
			'default'           => false,
			'sanitize_callback' => 'pulsepress_sanitize_checkbox',
		)
	);

	$wp_customize->add_control(
		'pulsepress_dark_mode',
		array(
			'label'    => esc_html__( 'Enable Dark Mode by Default', 'pulsepress' ),
			'section'  => 'pulsepress_color_scheme',
			'settings' => 'pulsepress_dark_mode',
			'type'     => 'checkbox',
		)
	);

	// Add Layout Section
	$wp_customize->add_section(
		'pulsepress_layout',
		array(
			'title'       => esc_html__( 'Layout', 'pulsepress' ),
			'description' => esc_html__( 'Configure the theme layout', 'pulsepress' ),
			'panel'       => 'pulsepress_theme_options',
			'priority'    => 20,
		)
	);

	// Container Width
	$wp_customize->add_setting(
		'pulsepress_container_width',
		array(
			'default'           => '1140',
			'sanitize_callback' => 'absint',
			'transport'         => 'postMessage',
		)
	);

	$wp_customize->add_control(
		'pulsepress_container_width',
		array(
			'label'       => esc_html__( 'Container Width (px)', 'pulsepress' ),
			'section'     => 'pulsepress_layout',
			'settings'    => 'pulsepress_container_width',
			'type'        => 'number',
			'input_attrs' => array(
				'min'  => 960,
				'max'  => 1600,
				'step' => 10,
			),
		)
	);

	// Sidebar Position
	$wp_customize->add_setting(
		'pulsepress_sidebar_position',
		array(
			'default'           => 'right',
			'sanitize_callback' => 'pulsepress_sanitize_select',
		)
	);

	$wp_customize->add_control(
		'pulsepress_sidebar_position',
		array(
			'label'    => esc_html__( 'Sidebar Position', 'pulsepress' ),
			'section'  => 'pulsepress_layout',
			'settings' => 'pulsepress_sidebar_position',
			'type'     => 'select',
			'choices'  => array(
				'right' => esc_html__( 'Right Sidebar', 'pulsepress' ),
				'left'  => esc_html__( 'Left Sidebar', 'pulsepress' ),
				'none'  => esc_html__( 'No Sidebar', 'pulsepress' ),
			),
		)
	);

	// Add Typography Section
	$wp_customize->add_section(
		'pulsepress_typography',
		array(
			'title'       => esc_html__( 'Typography', 'pulsepress' ),
			'description' => esc_html__( 'Configure the theme typography', 'pulsepress' ),
			'panel'       => 'pulsepress_theme_options',
			'priority'    => 30,
		)
	);

	// Body Font
	$wp_customize->add_setting(
		'pulsepress_body_font',
		array(
			'default'           => 'Roboto',
			'sanitize_callback' => 'sanitize_text_field',
		)
	);

	$wp_customize->add_control(
		'pulsepress_body_font',
		array(
			'label'    => esc_html__( 'Body Font', 'pulsepress' ),
			'section'  => 'pulsepress_typography',
			'settings' => 'pulsepress_body_font',
			'type'     => 'select',
			'choices'  => array(
				'Roboto'      => esc_html__( 'Roboto', 'pulsepress' ),
				'Open Sans'   => esc_html__( 'Open Sans', 'pulsepress' ),
				'Lato'        => esc_html__( 'Lato', 'pulsepress' ),
				'Montserrat'  => esc_html__( 'Montserrat', 'pulsepress' ),
				'Source Sans Pro' => esc_html__( 'Source Sans Pro', 'pulsepress' ),
			),
		)
	);

	// Heading Font
	$wp_customize->add_setting(
		'pulsepress_heading_font',
		array(
			'default'           => 'Montserrat',
			'sanitize_callback' => 'sanitize_text_field',
		)
	);

	$wp_customize->add_control(
		'pulsepress_heading_font',
		array(
			'label'    => esc_html__( 'Heading Font', 'pulsepress' ),
			'section'  => 'pulsepress_typography',
			'settings' => 'pulsepress_heading_font',
			'type'     => 'select',
			'choices'  => array(
				'Montserrat'  => esc_html__( 'Montserrat', 'pulsepress' ),
				'Roboto'      => esc_html__( 'Roboto', 'pulsepress' ),
				'Open Sans'   => esc_html__( 'Open Sans', 'pulsepress' ),
				'Lato'        => esc_html__( 'Lato', 'pulsepress' ),
				'Playfair Display' => esc_html__( 'Playfair Display', 'pulsepress' ),
			),
		)
	);

	// Add Social Media Section
	$wp_customize->add_section(
		'pulsepress_social_media',
		array(
			'title'       => esc_html__( 'Social Media', 'pulsepress' ),
			'description' => esc_html__( 'Configure social media integration', 'pulsepress' ),
			'panel'       => 'pulsepress_theme_options',
			'priority'    => 40,
		)
	);

	// Facebook
	$wp_customize->add_setting(
		'pulsepress_facebook',
		array(
			'default'           => '',
			'sanitize_callback' => 'esc_url_raw',
		)
	);

	$wp_customize->add_control(
		'pulsepress_facebook',
		array(
			'label'    => esc_html__( 'Facebook Page URL', 'pulsepress' ),
			'section'  => 'pulsepress_social_media',
			'settings' => 'pulsepress_facebook',
			'type'     => 'url',
		)
	);

	// Twitter
	$wp_customize->add_setting(
		'pulsepress_twitter',
		array(
			'default'           => '',
			'sanitize_callback' => 'esc_url_raw',
		)
	);

	$wp_customize->add_control(
		'pulsepress_twitter',
		array(
			'label'    => esc_html__( 'Twitter Profile URL', 'pulsepress' ),
			'section'  => 'pulsepress_social_media',
			'settings' => 'pulsepress_twitter',
			'type'     => 'url',
		)
	);

	// Instagram
	$wp_customize->add_setting(
		'pulsepress_instagram',
		array(
			'default'           => '',
			'sanitize_callback' => 'esc_url_raw',
		)
	);

	$wp_customize->add_control(
		'pulsepress_instagram',
		array(
			'label'    => esc_html__( 'Instagram Profile URL', 'pulsepress' ),
			'section'  => 'pulsepress_social_media',
			'settings' => 'pulsepress_instagram',
			'type'     => 'url',
		)
	);

	// Pinterest
	$wp_customize->add_setting(
		'pulsepress_pinterest',
		array(
			'default'           => '',
			'sanitize_callback' => 'esc_url_raw',
		)
	);

	$wp_customize->add_control(
		'pulsepress_pinterest',
		array(
			'label'    => esc_html__( 'Pinterest Profile URL', 'pulsepress' ),
			'section'  => 'pulsepress_social_media',
			'settings' => 'pulsepress_pinterest',
			'type'     => 'url',
		)
	);

	// Auto-Share Posts
	$wp_customize->add_setting(
		'pulsepress_auto_share',
		array(
			'default'           => false,
			'sanitize_callback' => 'pulsepress_sanitize_checkbox',
		)
	);

	$wp_customize->add_control(
		'pulsepress_auto_share',
		array(
			'label'    => esc_html__( 'Auto-Share Posts to Social Media', 'pulsepress' ),
			'section'  => 'pulsepress_social_media',
			'settings' => 'pulsepress_auto_share',
			'type'     => 'checkbox',
		)
	);
}
add_action( 'customize_register', 'pulsepress_customize_register' );

/**
 * Render the site title for the selective refresh partial.
 *
 * @return void
 */
function pulsepress_customize_partial_blogname() {
	bloginfo( 'name' );
}

/**
 * Render the site tagline for the selective refresh partial.
 *
 * @return void
 */
function pulsepress_customize_partial_blogdescription() {
	bloginfo( 'description' );
}

/**
 * Binds JS handlers to make Theme Customizer preview reload changes asynchronously.
 */
function pulsepress_customize_preview_js() {
	wp_enqueue_script( 'pulsepress-customizer', get_template_directory_uri() . '/assets/js/customizer.js', array( 'customize-preview' ), PULSEPRESS_VERSION, true );
}
add_action( 'customize_preview_init', 'pulsepress_customize_preview_js' );

// pulsepress_sanitize_checkbox function moved to functions.php

/**
 * Sanitize select values.
 *
 * @param string $input The input from the setting.
 * @param object $setting The selected setting.
 * @return string The sanitized input.
 */
function pulsepress_sanitize_select( $input, $setting ) {
	// Get list of choices from the control associated with the setting.
	$choices = $setting->manager->get_control( $setting->id )->choices;

	// If the input is a valid key, return it; otherwise, return the default.
	return ( array_key_exists( $input, $choices ) ? $input : $setting->default );
}

/**
 * Output the custom CSS based on customizer settings.
 */
function pulsepress_customizer_css() {
	$primary_color = get_theme_mod( 'pulsepress_primary_color', '#1e73be' );
	$secondary_color = get_theme_mod( 'pulsepress_secondary_color', '#ff6b6b' );
	$container_width = get_theme_mod( 'pulsepress_container_width', '1140' );
	$body_font = get_theme_mod( 'pulsepress_body_font', 'Roboto' );
	$heading_font = get_theme_mod( 'pulsepress_heading_font', 'Montserrat' );
	
	$css = '';
	
	// Primary Color
	if ( '#1e73be' !== $primary_color ) {
		$css .= ':root { --primary-color: ' . esc_attr( $primary_color ) . '; }';
	}
	
	// Secondary Color
	if ( '#ff6b6b' !== $secondary_color ) {
		$css .= ':root { --secondary-color: ' . esc_attr( $secondary_color ) . '; }';
	}
	
	// Container Width
	if ( '1140' !== $container_width ) {
		$css .= ':root { --container-xl: ' . esc_attr( $container_width ) . 'px; }';
	}
	
	// Body Font
	if ( 'Roboto' !== $body_font ) {
		$css .= ':root { --body-font: "' . esc_attr( $body_font ) . '", sans-serif; }';
	}
	
	// Heading Font
	if ( 'Montserrat' !== $heading_font ) {
		$css .= ':root { --heading-font: "' . esc_attr( $heading_font ) . '", sans-serif; }';
	}
	
	// Sidebar Position
	$sidebar_position = get_theme_mod( 'pulsepress_sidebar_position', 'right' );
	if ( 'left' === $sidebar_position ) {
		$css .= '
		@media (min-width: 992px) {
			.content-area {
				order: 2;
			}
			.widget-area {
				order: 1;
			}
		}';
	} elseif ( 'none' === $sidebar_position ) {
		$css .= '
		@media (min-width: 992px) {
			.content-area {
				flex: 0 0 100%;
				max-width: 100%;
			}
			.widget-area {
				display: none;
			}
		}';
	}
	
	if ( ! empty( $css ) ) {
		wp_add_inline_style( 'pulsepress-style', $css );
	}
}
add_action( 'wp_enqueue_scripts', 'pulsepress_customizer_css' );
