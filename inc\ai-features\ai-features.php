<?php
/**
 * AI-Powered Features
 *
 * @package PulsePress
 */

// Prevent direct access
if ( ! defined( 'ABSPATH' ) ) {
    exit;
}

/**
 * Initialize AI features
 */
function pulsepress_init_ai_features() {
    add_action( 'save_post', 'pulsepress_auto_generate_summary', 10, 2 );
    add_action( 'save_post', 'pulsepress_auto_tag_post', 10, 2 );
    add_action( 'wp_ajax_generate_summary', 'pulsepress_ajax_generate_summary' );
    add_action( 'wp_ajax_nopriv_generate_summary', 'pulsepress_ajax_generate_summary' );
    add_action( 'add_meta_boxes', 'pulsepress_add_ai_meta_boxes' );
    add_shortcode( 'post_summary', 'pulsepress_post_summary_shortcode' );
}
add_action( 'init', 'pulsepress_init_ai_features' );

/**
 * Add AI meta boxes to post editor
 */
function pulsepress_add_ai_meta_boxes() {
    add_meta_box(
        'pulsepress-ai-features',
        esc_html__( 'AI Features', 'pulsepress' ),
        'pulsepress_ai_meta_box_callback',
        'post',
        'side',
        'default'
    );
}

/**
 * AI meta box callback
 */
function pulsepress_ai_meta_box_callback( $post ) {
    wp_nonce_field( 'pulsepress_ai_meta_box', 'pulsepress_ai_meta_box_nonce' );
    
    $auto_summary = get_post_meta( $post->ID, '_pulsepress_auto_summary', true );
    $reading_time = get_post_meta( $post->ID, '_pulsepress_reading_time', true );
    $ai_tags = get_post_meta( $post->ID, '_pulsepress_ai_tags', true );
    
    ?>
    <div class="ai-features-panel">
        <h4><?php esc_html_e( 'Auto-Generated Summary', 'pulsepress' ); ?></h4>
        <textarea id="auto-summary" name="auto_summary" rows="4" style="width: 100%;"><?php echo esc_textarea( $auto_summary ); ?></textarea>
        <button type="button" id="generate-summary" class="button"><?php esc_html_e( 'Generate Summary', 'pulsepress' ); ?></button>
        
        <h4><?php esc_html_e( 'Reading Time', 'pulsepress' ); ?></h4>
        <p><strong><?php echo esc_html( $reading_time ? $reading_time . ' min read' : 'Not calculated' ); ?></strong></p>
        
        <h4><?php esc_html_e( 'AI Suggested Tags', 'pulsepress' ); ?></h4>
        <div id="ai-tags">
            <?php if ( $ai_tags ) : ?>
                <?php foreach ( explode( ',', $ai_tags ) as $tag ) : ?>
                    <span class="ai-tag"><?php echo esc_html( trim( $tag ) ); ?></span>
                <?php endforeach; ?>
            <?php endif; ?>
        </div>
        <button type="button" id="generate-tags" class="button"><?php esc_html_e( 'Generate Tags', 'pulsepress' ); ?></button>
    </div>
    
    <style>
    .ai-features-panel h4 { margin-top: 15px; margin-bottom: 5px; }
    .ai-tag { 
        display: inline-block; 
        background: #0073aa; 
        color: white; 
        padding: 2px 8px; 
        margin: 2px; 
        border-radius: 3px; 
        font-size: 11px; 
    }
    </style>
    
    <script>
    jQuery(document).ready(function($) {
        $('#generate-summary').click(function() {
            var content = $('#content').val() || tinymce.get('content').getContent();
            if (!content) {
                alert('<?php esc_html_e( 'Please add some content first.', 'pulsepress' ); ?>');
                return;
            }
            
            $(this).prop('disabled', true).text('<?php esc_html_e( 'Generating...', 'pulsepress' ); ?>');
            
            $.ajax({
                url: ajaxurl,
                type: 'POST',
                data: {
                    action: 'generate_summary',
                    content: content,
                    nonce: '<?php echo wp_create_nonce( 'generate_summary_nonce' ); ?>'
                },
                success: function(response) {
                    if (response.success) {
                        $('#auto-summary').val(response.data.summary);
                    } else {
                        alert('<?php esc_html_e( 'Failed to generate summary.', 'pulsepress' ); ?>');
                    }
                },
                complete: function() {
                    $('#generate-summary').prop('disabled', false).text('<?php esc_html_e( 'Generate Summary', 'pulsepress' ); ?>');
                }
            });
        });
        
        $('#generate-tags').click(function() {
            var content = $('#content').val() || tinymce.get('content').getContent();
            var title = $('#title').val();
            
            if (!content && !title) {
                alert('<?php esc_html_e( 'Please add a title and content first.', 'pulsepress' ); ?>');
                return;
            }
            
            $(this).prop('disabled', true).text('<?php esc_html_e( 'Generating...', 'pulsepress' ); ?>');
            
            $.ajax({
                url: ajaxurl,
                type: 'POST',
                data: {
                    action: 'generate_tags',
                    content: content,
                    title: title,
                    nonce: '<?php echo wp_create_nonce( 'generate_tags_nonce' ); ?>'
                },
                success: function(response) {
                    if (response.success) {
                        $('#ai-tags').html(response.data.tags_html);
                    } else {
                        alert('<?php esc_html_e( 'Failed to generate tags.', 'pulsepress' ); ?>');
                    }
                },
                complete: function() {
                    $('#generate-tags').prop('disabled', false).text('<?php esc_html_e( 'Generate Tags', 'pulsepress' ); ?>');
                }
            });
        });
    });
    </script>
    <?php
}

/**
 * Auto-generate summary when post is saved
 */
function pulsepress_auto_generate_summary( $post_id, $post ) {
    if ( ! get_theme_mod( 'pulsepress_auto_ai_summary', false ) ) {
        return;
    }
    
    if ( wp_is_post_revision( $post_id ) || $post->post_type !== 'post' ) {
        return;
    }
    
    $content = $post->post_content;
    if ( empty( $content ) ) {
        return;
    }
    
    $summary = pulsepress_generate_content_summary( $content );
    if ( $summary ) {
        update_post_meta( $post_id, '_pulsepress_auto_summary', $summary );
    }
    
    // Calculate reading time
    $reading_time = pulsepress_calculate_reading_time( $content );
    update_post_meta( $post_id, '_pulsepress_reading_time', $reading_time );
}

/**
 * Auto-tag posts using AI
 */
function pulsepress_auto_tag_post( $post_id, $post ) {
    if ( ! get_theme_mod( 'pulsepress_auto_ai_tagging', false ) ) {
        return;
    }
    
    if ( wp_is_post_revision( $post_id ) || $post->post_type !== 'post' ) {
        return;
    }
    
    $content = $post->post_content . ' ' . $post->post_title;
    $tags = pulsepress_generate_ai_tags( $content );
    
    if ( $tags ) {
        update_post_meta( $post_id, '_pulsepress_ai_tags', implode( ',', $tags ) );
        
        // Optionally auto-apply tags
        if ( get_theme_mod( 'pulsepress_auto_apply_ai_tags', false ) ) {
            wp_set_post_tags( $post_id, $tags, true );
        }
    }
}

/**
 * AJAX handler for generating summary
 */
function pulsepress_ajax_generate_summary() {
    check_ajax_referer( 'generate_summary_nonce', 'nonce' );
    
    $content = sanitize_textarea_field( $_POST['content'] );
    $summary = pulsepress_generate_content_summary( $content );
    
    if ( $summary ) {
        wp_send_json_success( array( 'summary' => $summary ) );
    } else {
        wp_send_json_error( array( 'message' => 'Failed to generate summary' ) );
    }
}

/**
 * Generate content summary using AI or fallback method
 */
function pulsepress_generate_content_summary( $content ) {
    // Remove HTML tags and get plain text
    $text = wp_strip_all_tags( $content );
    $text = preg_replace( '/\s+/', ' ', $text );
    
    // Try AI API first (OpenAI, Claude, etc.)
    $api_key = get_theme_mod( 'pulsepress_ai_api_key', '' );
    if ( ! empty( $api_key ) ) {
        $ai_summary = pulsepress_ai_api_summarize( $text, $api_key );
        if ( $ai_summary ) {
            return $ai_summary;
        }
    }
    
    // Fallback: Simple extractive summary
    return pulsepress_extractive_summary( $text );
}

/**
 * AI API summarization (placeholder for actual API integration)
 */
function pulsepress_ai_api_summarize( $text, $api_key ) {
    // This is a placeholder - implement actual API calls to OpenAI, Claude, etc.
    // For now, return false to use fallback method
    return false;
}

/**
 * Simple extractive summary fallback
 */
function pulsepress_extractive_summary( $text, $sentences = 3 ) {
    $sentences_array = preg_split( '/[.!?]+/', $text );
    $sentences_array = array_filter( array_map( 'trim', $sentences_array ) );
    
    if ( count( $sentences_array ) <= $sentences ) {
        return implode( '. ', $sentences_array ) . '.';
    }
    
    // Score sentences by word frequency and position
    $word_freq = array();
    $words = str_word_count( strtolower( $text ), 1 );
    foreach ( $words as $word ) {
        if ( strlen( $word ) > 3 ) {
            $word_freq[ $word ] = ( $word_freq[ $word ] ?? 0 ) + 1;
        }
    }
    
    $sentence_scores = array();
    foreach ( $sentences_array as $i => $sentence ) {
        $score = 0;
        $sentence_words = str_word_count( strtolower( $sentence ), 1 );
        foreach ( $sentence_words as $word ) {
            if ( isset( $word_freq[ $word ] ) ) {
                $score += $word_freq[ $word ];
            }
        }
        // Boost score for early sentences
        $score *= ( 1 + ( 1 / ( $i + 1 ) ) );
        $sentence_scores[ $i ] = $score;
    }
    
    arsort( $sentence_scores );
    $top_sentences = array_slice( array_keys( $sentence_scores ), 0, $sentences, true );
    sort( $top_sentences );
    
    $summary = array();
    foreach ( $top_sentences as $i ) {
        $summary[] = $sentences_array[ $i ];
    }
    
    return implode( '. ', $summary ) . '.';
}

/**
 * Generate AI tags
 */
function pulsepress_generate_ai_tags( $content ) {
    // Simple keyword extraction as fallback
    $text = wp_strip_all_tags( $content );
    $words = str_word_count( strtolower( $text ), 1 );
    
    // Filter out common words
    $stop_words = array( 'the', 'and', 'or', 'but', 'in', 'on', 'at', 'to', 'for', 'of', 'with', 'by', 'is', 'are', 'was', 'were', 'be', 'been', 'have', 'has', 'had', 'do', 'does', 'did', 'will', 'would', 'could', 'should', 'may', 'might', 'must', 'can', 'this', 'that', 'these', 'those', 'a', 'an' );
    
    $word_freq = array();
    foreach ( $words as $word ) {
        if ( strlen( $word ) > 3 && ! in_array( $word, $stop_words ) ) {
            $word_freq[ $word ] = ( $word_freq[ $word ] ?? 0 ) + 1;
        }
    }
    
    arsort( $word_freq );
    return array_slice( array_keys( $word_freq ), 0, 8 );
}

/**
 * Calculate reading time
 */
function pulsepress_calculate_reading_time( $content ) {
    $word_count = str_word_count( wp_strip_all_tags( $content ) );
    $reading_speed = 200; // words per minute
    return max( 1, round( $word_count / $reading_speed ) );
}

/**
 * Post summary shortcode
 */
function pulsepress_post_summary_shortcode( $atts ) {
    $atts = shortcode_atts( array(
        'post_id' => get_the_ID(),
        'fallback' => true,
    ), $atts );
    
    $summary = get_post_meta( $atts['post_id'], '_pulsepress_auto_summary', true );
    
    if ( empty( $summary ) && $atts['fallback'] ) {
        $post = get_post( $atts['post_id'] );
        if ( $post ) {
            $summary = pulsepress_extractive_summary( $post->post_content, 2 );
        }
    }
    
    if ( $summary ) {
        return '<div class="post-summary"><strong>' . esc_html__( 'Summary:', 'pulsepress' ) . '</strong> ' . esc_html( $summary ) . '</div>';
    }
    
    return '';
}

/**
 * Add AI settings to customizer
 */
function pulsepress_ai_customizer( $wp_customize ) {
    // AI Features Section
    $wp_customize->add_section(
        'pulsepress_ai_features',
        array(
            'title'    => esc_html__( 'AI Features', 'pulsepress' ),
            'priority' => 170,
        )
    );
    
    // AI API Key
    $wp_customize->add_setting(
        'pulsepress_ai_api_key',
        array(
            'default'           => '',
            'sanitize_callback' => 'sanitize_text_field',
        )
    );
    
    $wp_customize->add_control(
        'pulsepress_ai_api_key',
        array(
            'label'       => esc_html__( 'AI API Key', 'pulsepress' ),
            'description' => esc_html__( 'Enter your OpenAI or other AI service API key', 'pulsepress' ),
            'section'     => 'pulsepress_ai_features',
            'type'        => 'password',
        )
    );
    
    // Auto AI Summary
    $wp_customize->add_setting(
        'pulsepress_auto_ai_summary',
        array(
            'default'           => false,
            'sanitize_callback' => 'pulsepress_sanitize_checkbox',
        )
    );
    
    $wp_customize->add_control(
        'pulsepress_auto_ai_summary',
        array(
            'label'   => esc_html__( 'Auto-Generate Summaries', 'pulsepress' ),
            'section' => 'pulsepress_ai_features',
            'type'    => 'checkbox',
        )
    );
    
    // Auto AI Tagging
    $wp_customize->add_setting(
        'pulsepress_auto_ai_tagging',
        array(
            'default'           => false,
            'sanitize_callback' => 'pulsepress_sanitize_checkbox',
        )
    );
    
    $wp_customize->add_control(
        'pulsepress_auto_ai_tagging',
        array(
            'label'   => esc_html__( 'Auto-Generate Tags', 'pulsepress' ),
            'section' => 'pulsepress_ai_features',
            'type'    => 'checkbox',
        )
    );
}
add_action( 'customize_register', 'pulsepress_ai_customizer' );
