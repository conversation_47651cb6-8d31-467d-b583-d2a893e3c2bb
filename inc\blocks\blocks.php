<?php
/**
 * Custom Gutenberg Blocks
 *
 * @package PulsePress
 */

// Prevent direct access
if ( ! defined( 'ABSPATH' ) ) {
    exit;
}

/**
 * Initialize custom blocks
 */
function pulsepress_init_blocks() {
    add_action( 'init', 'pulsepress_register_blocks' );
    add_action( 'enqueue_block_editor_assets', 'pulsepress_enqueue_block_editor_assets' );
    add_action( 'wp_enqueue_scripts', 'pulsepress_enqueue_block_assets' );
}
add_action( 'init', 'pulsepress_init_blocks' );

/**
 * Register custom blocks
 */
function pulsepress_register_blocks() {
    // Check if Gutenberg is available
    if ( ! function_exists( 'register_block_type' ) ) {
        return;
    }
    
    // Register Editor's Picks block
    register_block_type( 'pulsepress/editors-picks', array(
        'editor_script'   => 'pulsepress-blocks-editor',
        'editor_style'    => 'pulsepress-blocks-editor',
        'style'           => 'pulsepress-blocks',
        'render_callback' => 'pulsepress_render_editors_picks_block',
        'attributes'      => array(
            'numberOfPosts' => array(
                'type'    => 'number',
                'default' => 4,
            ),
            'showExcerpt' => array(
                'type'    => 'boolean',
                'default' => true,
            ),
            'showDate' => array(
                'type'    => 'boolean',
                'default' => true,
            ),
            'showAuthor' => array(
                'type'    => 'boolean',
                'default' => true,
            ),
        ),
    ) );
    
    // Register Weekly Top Stories block
    register_block_type( 'pulsepress/weekly-top-stories', array(
        'editor_script'   => 'pulsepress-blocks-editor',
        'editor_style'    => 'pulsepress-blocks-editor',
        'style'           => 'pulsepress-blocks',
        'render_callback' => 'pulsepress_render_weekly_top_stories_block',
        'attributes'      => array(
            'numberOfPosts' => array(
                'type'    => 'number',
                'default' => 5,
            ),
            'timeframe' => array(
                'type'    => 'string',
                'default' => 'week',
            ),
        ),
    ) );
    
    // Register Featured Category block
    register_block_type( 'pulsepress/featured-category', array(
        'editor_script'   => 'pulsepress-blocks-editor',
        'editor_style'    => 'pulsepress-blocks-editor',
        'style'           => 'pulsepress-blocks',
        'render_callback' => 'pulsepress_render_featured_category_block',
        'attributes'      => array(
            'categoryId' => array(
                'type'    => 'number',
                'default' => 0,
            ),
            'numberOfPosts' => array(
                'type'    => 'number',
                'default' => 6,
            ),
            'layout' => array(
                'type'    => 'string',
                'default' => 'grid',
            ),
            'showCategoryTitle' => array(
                'type'    => 'boolean',
                'default' => true,
            ),
        ),
    ) );
    
    // Register Breaking News block
    register_block_type( 'pulsepress/breaking-news', array(
        'editor_script'   => 'pulsepress-blocks-editor',
        'editor_style'    => 'pulsepress-blocks-editor',
        'style'           => 'pulsepress-blocks',
        'render_callback' => 'pulsepress_render_breaking_news_block',
        'attributes'      => array(
            'title' => array(
                'type'    => 'string',
                'default' => 'Breaking News',
            ),
            'content' => array(
                'type'    => 'string',
                'default' => '',
            ),
            'link' => array(
                'type'    => 'string',
                'default' => '',
            ),
            'urgent' => array(
                'type'    => 'boolean',
                'default' => false,
            ),
        ),
    ) );
}

/**
 * Enqueue block editor assets
 */
function pulsepress_enqueue_block_editor_assets() {
    wp_enqueue_script(
        'pulsepress-blocks-editor',
        get_template_directory_uri() . '/assets/js/blocks-editor.js',
        array( 'wp-blocks', 'wp-i18n', 'wp-element', 'wp-components', 'wp-editor' ),
        PULSEPRESS_VERSION,
        true
    );
    
    wp_enqueue_style(
        'pulsepress-blocks-editor',
        get_template_directory_uri() . '/assets/css/blocks-editor.css',
        array( 'wp-edit-blocks' ),
        PULSEPRESS_VERSION
    );
    
    // Localize script with data
    wp_localize_script(
        'pulsepress-blocks-editor',
        'pulsepressBlocks',
        array(
            'categories' => pulsepress_get_categories_for_blocks(),
        )
    );
}

/**
 * Enqueue block assets for frontend
 */
function pulsepress_enqueue_block_assets() {
    wp_enqueue_style(
        'pulsepress-blocks',
        get_template_directory_uri() . '/assets/css/blocks.css',
        array(),
        PULSEPRESS_VERSION
    );
}

/**
 * Render Editor's Picks block
 */
function pulsepress_render_editors_picks_block( $attributes ) {
    $number_of_posts = $attributes['numberOfPosts'] ?? 4;
    $show_excerpt = $attributes['showExcerpt'] ?? true;
    $show_date = $attributes['showDate'] ?? true;
    $show_author = $attributes['showAuthor'] ?? true;
    
    $query = new WP_Query( array(
        'post_type'      => 'post',
        'posts_per_page' => $number_of_posts,
        'meta_query'     => array(
            array(
                'key'     => '_pulsepress_editors_pick',
                'value'   => '1',
                'compare' => '=',
            ),
        ),
    ) );
    
    if ( ! $query->have_posts() ) {
        // Fallback to latest posts
        $query = new WP_Query( array(
            'post_type'      => 'post',
            'posts_per_page' => $number_of_posts,
        ) );
    }
    
    ob_start();
    ?>
    <div class="wp-block-pulsepress-editors-picks">
        <h2 class="block-title"><?php esc_html_e( "Editor's Picks", 'pulsepress' ); ?></h2>
        <div class="editors-picks-grid">
            <?php
            while ( $query->have_posts() ) {
                $query->the_post();
                ?>
                <article class="editors-pick-item">
                    <?php if ( has_post_thumbnail() ) : ?>
                        <div class="post-thumbnail">
                            <a href="<?php the_permalink(); ?>">
                                <?php the_post_thumbnail( 'pulsepress-grid' ); ?>
                            </a>
                        </div>
                    <?php endif; ?>
                    
                    <div class="post-content">
                        <h3 class="post-title">
                            <a href="<?php the_permalink(); ?>"><?php the_title(); ?></a>
                        </h3>
                        
                        <?php if ( $show_excerpt ) : ?>
                            <div class="post-excerpt">
                                <?php echo wp_trim_words( get_the_excerpt(), 20 ); ?>
                            </div>
                        <?php endif; ?>
                        
                        <div class="post-meta">
                            <?php if ( $show_author ) : ?>
                                <span class="post-author">
                                    <?php esc_html_e( 'By', 'pulsepress' ); ?> 
                                    <a href="<?php echo esc_url( get_author_posts_url( get_the_author_meta( 'ID' ) ) ); ?>">
                                        <?php the_author(); ?>
                                    </a>
                                </span>
                            <?php endif; ?>
                            
                            <?php if ( $show_date ) : ?>
                                <span class="post-date">
                                    <?php echo get_the_date(); ?>
                                </span>
                            <?php endif; ?>
                        </div>
                    </div>
                </article>
                <?php
            }
            wp_reset_postdata();
            ?>
        </div>
    </div>
    <?php
    return ob_get_clean();
}

/**
 * Render Weekly Top Stories block
 */
function pulsepress_render_weekly_top_stories_block( $attributes ) {
    $number_of_posts = $attributes['numberOfPosts'] ?? 5;
    $timeframe = $attributes['timeframe'] ?? 'week';
    
    $date_query = array();
    switch ( $timeframe ) {
        case 'day':
            $date_query = array(
                'after' => '1 day ago',
            );
            break;
        case 'week':
            $date_query = array(
                'after' => '1 week ago',
            );
            break;
        case 'month':
            $date_query = array(
                'after' => '1 month ago',
            );
            break;
    }
    
    $query = new WP_Query( array(
        'post_type'      => 'post',
        'posts_per_page' => $number_of_posts,
        'meta_key'       => 'post_views_count',
        'orderby'        => 'meta_value_num',
        'order'          => 'DESC',
        'date_query'     => $date_query,
    ) );
    
    ob_start();
    ?>
    <div class="wp-block-pulsepress-weekly-top-stories">
        <h2 class="block-title">
            <?php
            switch ( $timeframe ) {
                case 'day':
                    esc_html_e( 'Top Stories Today', 'pulsepress' );
                    break;
                case 'week':
                    esc_html_e( 'Weekly Top Stories', 'pulsepress' );
                    break;
                case 'month':
                    esc_html_e( 'Monthly Top Stories', 'pulsepress' );
                    break;
            }
            ?>
        </h2>
        <div class="top-stories-list">
            <?php
            $counter = 1;
            while ( $query->have_posts() ) {
                $query->the_post();
                ?>
                <article class="top-story-item">
                    <span class="story-number"><?php echo esc_html( $counter ); ?></span>
                    <div class="story-content">
                        <h3 class="story-title">
                            <a href="<?php the_permalink(); ?>"><?php the_title(); ?></a>
                        </h3>
                        <div class="story-meta">
                            <span class="story-views">
                                <?php
                                $views = get_post_meta( get_the_ID(), 'post_views_count', true );
                                echo esc_html( number_format( intval( $views ) ) . ' views' );
                                ?>
                            </span>
                            <span class="story-date"><?php echo get_the_date(); ?></span>
                        </div>
                    </div>
                </article>
                <?php
                $counter++;
            }
            wp_reset_postdata();
            ?>
        </div>
    </div>
    <?php
    return ob_get_clean();
}

/**
 * Render Featured Category block
 */
function pulsepress_render_featured_category_block( $attributes ) {
    $category_id = $attributes['categoryId'] ?? 0;
    $number_of_posts = $attributes['numberOfPosts'] ?? 6;
    $layout = $attributes['layout'] ?? 'grid';
    $show_category_title = $attributes['showCategoryTitle'] ?? true;
    
    if ( ! $category_id ) {
        return '<p>' . esc_html__( 'Please select a category.', 'pulsepress' ) . '</p>';
    }
    
    $category = get_category( $category_id );
    if ( ! $category ) {
        return '<p>' . esc_html__( 'Category not found.', 'pulsepress' ) . '</p>';
    }
    
    $query = new WP_Query( array(
        'post_type'      => 'post',
        'posts_per_page' => $number_of_posts,
        'cat'            => $category_id,
    ) );
    
    ob_start();
    ?>
    <div class="wp-block-pulsepress-featured-category layout-<?php echo esc_attr( $layout ); ?>">
        <?php if ( $show_category_title ) : ?>
            <h2 class="block-title">
                <a href="<?php echo esc_url( get_category_link( $category_id ) ); ?>">
                    <?php echo esc_html( $category->name ); ?>
                </a>
            </h2>
        <?php endif; ?>
        
        <div class="featured-category-posts">
            <?php
            while ( $query->have_posts() ) {
                $query->the_post();
                ?>
                <article class="featured-post-item">
                    <?php if ( has_post_thumbnail() ) : ?>
                        <div class="post-thumbnail">
                            <a href="<?php the_permalink(); ?>">
                                <?php the_post_thumbnail( $layout === 'list' ? 'pulsepress-thumbnail' : 'pulsepress-grid' ); ?>
                            </a>
                        </div>
                    <?php endif; ?>
                    
                    <div class="post-content">
                        <h3 class="post-title">
                            <a href="<?php the_permalink(); ?>"><?php the_title(); ?></a>
                        </h3>
                        
                        <?php if ( $layout === 'grid' ) : ?>
                            <div class="post-excerpt">
                                <?php echo wp_trim_words( get_the_excerpt(), 15 ); ?>
                            </div>
                        <?php endif; ?>
                        
                        <div class="post-meta">
                            <span class="post-date"><?php echo get_the_date(); ?></span>
                        </div>
                    </div>
                </article>
                <?php
            }
            wp_reset_postdata();
            ?>
        </div>
        
        <div class="view-all-link">
            <a href="<?php echo esc_url( get_category_link( $category_id ) ); ?>" class="btn btn-primary">
                <?php printf( esc_html__( 'View All %s', 'pulsepress' ), esc_html( $category->name ) ); ?>
            </a>
        </div>
    </div>
    <?php
    return ob_get_clean();
}

/**
 * Render Breaking News block
 */
function pulsepress_render_breaking_news_block( $attributes ) {
    $title = $attributes['title'] ?? 'Breaking News';
    $content = $attributes['content'] ?? '';
    $link = $attributes['link'] ?? '';
    $urgent = $attributes['urgent'] ?? false;
    
    if ( empty( $content ) ) {
        return '';
    }
    
    ob_start();
    ?>
    <div class="wp-block-pulsepress-breaking-news <?php echo $urgent ? 'urgent' : ''; ?>">
        <div class="breaking-news-content">
            <span class="breaking-news-label"><?php echo esc_html( $title ); ?></span>
            <div class="breaking-news-text">
                <?php if ( $link ) : ?>
                    <a href="<?php echo esc_url( $link ); ?>"><?php echo esc_html( $content ); ?></a>
                <?php else : ?>
                    <?php echo esc_html( $content ); ?>
                <?php endif; ?>
            </div>
        </div>
    </div>
    <?php
    return ob_get_clean();
}

/**
 * Get categories for blocks
 */
function pulsepress_get_categories_for_blocks() {
    $categories = get_categories( array(
        'hide_empty' => false,
    ) );
    
    $category_options = array();
    foreach ( $categories as $category ) {
        $category_options[] = array(
            'value' => $category->term_id,
            'label' => $category->name,
        );
    }
    
    return $category_options;
}

/**
 * Add custom block category
 */
function pulsepress_add_block_category( $categories ) {
    return array_merge(
        $categories,
        array(
            array(
                'slug'  => 'pulsepress',
                'title' => esc_html__( 'PulsePress', 'pulsepress' ),
                'icon'  => 'admin-site-alt3',
            ),
        )
    );
}
add_filter( 'block_categories_all', 'pulsepress_add_block_category' );
