<?php
/**
 * The template for displaying the footer
 *
 * Contains the closing of the #content div and all content after.
 *
 * @link https://developer.wordpress.org/themes/basics/template-files/#template-partials
 *
 * @package PulsePress
 */

?>

	<footer id="colophon" class="site-footer">
		<div class="footer-widgets">
			<div class="container">
				<div class="row">
					<div class="col-md-3 col-sm-6">
						<div class="footer-widget-area">
							<?php if ( is_active_sidebar( 'footer-1' ) ) : ?>
								<?php dynamic_sidebar( 'footer-1' ); ?>
							<?php else : ?>
								<div class="widget">
									<h2 class="widget-title"><?php esc_html_e( 'About Us', 'pulsepress' ); ?></h2>
									<div class="textwidget">
										<p><?php esc_html_e( 'PulsePress is a modern WordPress theme for news, magazines, and blogs. It features a clean design, responsive layout, and social media integration.', 'pulsepress' ); ?></p>
									</div>
								</div>
							<?php endif; ?>
						</div>
					</div>
					
					<div class="col-md-3 col-sm-6">
						<div class="footer-widget-area">
							<?php if ( is_active_sidebar( 'footer-2' ) ) : ?>
								<?php dynamic_sidebar( 'footer-2' ); ?>
							<?php else : ?>
								<div class="widget">
									<h2 class="widget-title"><?php esc_html_e( 'Categories', 'pulsepress' ); ?></h2>
									<ul>
										<?php
										wp_list_categories(
											array(
												'title_li'    => '',
												'number'      => 5,
												'orderby'     => 'count',
												'order'       => 'DESC',
												'show_count'  => true,
											)
										);
										?>
									</ul>
								</div>
							<?php endif; ?>
						</div>
					</div>
					
					<div class="col-md-3 col-sm-6">
						<div class="footer-widget-area">
							<?php if ( is_active_sidebar( 'footer-3' ) ) : ?>
								<?php dynamic_sidebar( 'footer-3' ); ?>
							<?php else : ?>
								<div class="widget">
									<h2 class="widget-title"><?php esc_html_e( 'Recent Posts', 'pulsepress' ); ?></h2>
									<ul>
										<?php
										$recent_posts = wp_get_recent_posts(
											array(
												'numberposts' => 5,
												'post_status' => 'publish',
											)
										);
										
										foreach ( $recent_posts as $post ) {
											echo '<li><a href="' . esc_url( get_permalink( $post['ID'] ) ) . '">' . esc_html( $post['post_title'] ) . '</a></li>';
										}
										wp_reset_postdata();
										?>
									</ul>
								</div>
							<?php endif; ?>
						</div>
					</div>
					
					<div class="col-md-3 col-sm-6">
						<div class="footer-widget-area">
							<?php if ( is_active_sidebar( 'footer-4' ) ) : ?>
								<?php dynamic_sidebar( 'footer-4' ); ?>
							<?php else : ?>
								<div class="widget">
									<h2 class="widget-title"><?php esc_html_e( 'Subscribe', 'pulsepress' ); ?></h2>
									<div class="textwidget">
										<p><?php esc_html_e( 'Subscribe to our newsletter to receive the latest news and updates.', 'pulsepress' ); ?></p>
										<form class="newsletter-form">
											<input type="email" placeholder="<?php esc_attr_e( 'Your Email', 'pulsepress' ); ?>" required>
											<button type="submit"><?php esc_html_e( 'Subscribe', 'pulsepress' ); ?></button>
										</form>
									</div>
								</div>
							<?php endif; ?>
						</div>
					</div>
				</div>
			</div>
		</div>
		
		<div class="footer-social">
			<div class="container">
				<div class="social-links">
					<?php
					if ( has_nav_menu( 'social' ) ) {
						wp_nav_menu(
							array(
								'theme_location' => 'social',
								'menu_class'     => 'social-menu',
								'link_before'    => '<span class="screen-reader-text">',
								'link_after'     => '</span>',
								'depth'          => 1,
							)
						);
					} else {
						?>
						<ul class="social-menu">
							<li><a href="#" target="_blank"><i class="fab fa-facebook-f"></i><span class="screen-reader-text"><?php esc_html_e( 'Facebook', 'pulsepress' ); ?></span></a></li>
							<li><a href="#" target="_blank"><i class="fab fa-twitter"></i><span class="screen-reader-text"><?php esc_html_e( 'Twitter', 'pulsepress' ); ?></span></a></li>
							<li><a href="#" target="_blank"><i class="fab fa-instagram"></i><span class="screen-reader-text"><?php esc_html_e( 'Instagram', 'pulsepress' ); ?></span></a></li>
							<li><a href="#" target="_blank"><i class="fab fa-pinterest-p"></i><span class="screen-reader-text"><?php esc_html_e( 'Pinterest', 'pulsepress' ); ?></span></a></li>
							<li><a href="#" target="_blank"><i class="fab fa-youtube"></i><span class="screen-reader-text"><?php esc_html_e( 'YouTube', 'pulsepress' ); ?></span></a></li>
						</ul>
						<?php
					}
					?>
				</div>
			</div>
		</div>
		
		<div class="site-info">
			<div class="container">
				<div class="copyright">
					<?php
					/* translators: %s: Current year and site name */
					printf( esc_html__( '© %s - All Rights Reserved', 'pulsepress' ), date_i18n( 'Y' ) . ' ' . get_bloginfo( 'name' ) );
					?>
				</div>
				
				<div class="footer-menu">
					<?php
					if ( has_nav_menu( 'footer' ) ) {
						wp_nav_menu(
							array(
								'theme_location' => 'footer',
								'menu_class'     => 'footer-links',
								'depth'          => 1,
							)
						);
					} else {
						?>
						<ul class="footer-links">
							<li><a href="#"><?php esc_html_e( 'Privacy Policy', 'pulsepress' ); ?></a></li>
							<li><a href="#"><?php esc_html_e( 'Terms of Service', 'pulsepress' ); ?></a></li>
							<li><a href="#"><?php esc_html_e( 'Contact Us', 'pulsepress' ); ?></a></li>
							<li><a href="#"><?php esc_html_e( 'Advertise', 'pulsepress' ); ?></a></li>
						</ul>
						<?php
					}
					?>
				</div>
			</div>
		</div>
	</footer><!-- #colophon -->
</div><!-- #page -->

<?php wp_footer(); ?>

</body>
</html>
