<?php
/**
 * The template for displaying the front page
 *
 * @link https://developer.wordpress.org/themes/basics/template-hierarchy/
 *
 * @package PulsePress
 */

get_header();
?>

<main id="primary" class="site-main">
	
	<!-- Hero Section with Featured Posts Slider -->
	<section class="hero-section">
		<div class="container">
			<div class="featured-slider">
				<?php
				// Get featured posts (posts with tag or category 'featured')
				$featured_args = array(
					'post_type'      => 'post',
					'posts_per_page' => 5,
					'tax_query'      => array(
						array(
							'taxonomy' => 'category',
							'field'    => 'slug',
							'terms'    => 'featured',
						),
					),
				);
				
				// If no featured category exists, get latest posts
				$featured_query = new WP_Query( $featured_args );
				
				if ( ! $featured_query->have_posts() ) {
					$featured_args = array(
						'post_type'      => 'post',
						'posts_per_page' => 5,
					);
					$featured_query = new WP_Query( $featured_args );
				}
				
				if ( $featured_query->have_posts() ) :
					while ( $featured_query->have_posts() ) :
						$featured_query->the_post();
						?>
						<div class="featured-slide">
							<div class="featured-image">
								<?php if ( has_post_thumbnail() ) : ?>
									<?php the_post_thumbnail( 'pulsepress-featured' ); ?>
								<?php else : ?>
									<img src="<?php echo esc_url( get_template_directory_uri() . '/assets/images/placeholder.jpg' ); ?>" alt="<?php the_title_attribute(); ?>">
								<?php endif; ?>
							</div>
							<div class="featured-content">
								<div class="post-category">
									<?php
									$categories = get_the_category();
									if ( ! empty( $categories ) ) {
										echo '<a href="' . esc_url( get_category_link( $categories[0]->term_id ) ) . '">' . esc_html( $categories[0]->name ) . '</a>';
									}
									?>
								</div>
								<h2 class="entry-title"><a href="<?php the_permalink(); ?>"><?php the_title(); ?></a></h2>
								<div class="entry-meta">
									<?php
									pulsepress_posted_by();
									pulsepress_posted_on();
									?>
								</div>
								<div class="entry-summary">
									<?php the_excerpt(); ?>
								</div>
								<a href="<?php the_permalink(); ?>" class="read-more"><?php esc_html_e( 'Read More', 'pulsepress' ); ?> <i class="fas fa-arrow-right"></i></a>
							</div>
						</div>
						<?php
					endwhile;
					wp_reset_postdata();
				endif;
				?>
			</div>
		</div>
	</section>
	
	<!-- Latest News Section -->
	<section class="latest-news-section section-padding">
		<div class="container">
			<div class="section-header">
				<h2 class="section-title"><?php esc_html_e( 'Latest News', 'pulsepress' ); ?></h2>
				<a href="<?php echo esc_url( get_permalink( get_option( 'page_for_posts' ) ) ); ?>" class="view-all"><?php esc_html_e( 'View All', 'pulsepress' ); ?> <i class="fas fa-arrow-right"></i></a>
			</div>
			
			<div class="row">
				<?php
				$latest_args = array(
					'post_type'      => 'post',
					'posts_per_page' => 6,
				);
				
				$latest_query = new WP_Query( $latest_args );
				
				if ( $latest_query->have_posts() ) :
					while ( $latest_query->have_posts() ) :
						$latest_query->the_post();
						?>
						<div class="col-lg-4 col-md-6">
							<article id="post-<?php the_ID(); ?>" <?php post_class( 'post-card' ); ?>>
								<div class="post-thumbnail">
									<?php if ( has_post_thumbnail() ) : ?>
										<a href="<?php the_permalink(); ?>">
											<?php the_post_thumbnail( 'pulsepress-grid' ); ?>
										</a>
									<?php else : ?>
										<a href="<?php the_permalink(); ?>">
											<img src="<?php echo esc_url( get_template_directory_uri() . '/assets/images/placeholder.jpg' ); ?>" alt="<?php the_title_attribute(); ?>">
										</a>
									<?php endif; ?>
									<div class="post-category">
										<?php
										$categories = get_the_category();
										if ( ! empty( $categories ) ) {
											echo '<a href="' . esc_url( get_category_link( $categories[0]->term_id ) ) . '">' . esc_html( $categories[0]->name ) . '</a>';
										}
										?>
									</div>
								</div>
								
								<div class="post-content">
									<h3 class="entry-title"><a href="<?php the_permalink(); ?>"><?php the_title(); ?></a></h3>
									<div class="entry-meta">
										<?php
										pulsepress_posted_by();
										pulsepress_posted_on();
										?>
									</div>
									<div class="entry-summary">
										<?php the_excerpt(); ?>
									</div>
								</div>
							</article>
						</div>
						<?php
					endwhile;
					wp_reset_postdata();
				endif;
				?>
			</div>
		</div>
	</section>
	
	<!-- Category Sections -->
	<?php
	// Get categories with most posts
	$categories = get_categories( array(
		'orderby'    => 'count',
		'order'      => 'DESC',
		'number'     => 4,
		'hide_empty' => true,
	) );
	
	foreach ( $categories as $category ) :
		?>
		<section class="category-section section-padding">
			<div class="container">
				<div class="section-header">
					<h2 class="section-title"><?php echo esc_html( $category->name ); ?></h2>
					<a href="<?php echo esc_url( get_category_link( $category->term_id ) ); ?>" class="view-all"><?php esc_html_e( 'View All', 'pulsepress' ); ?> <i class="fas fa-arrow-right"></i></a>
				</div>
				
				<div class="row">
					<?php
					$category_args = array(
						'post_type'      => 'post',
						'posts_per_page' => 4,
						'cat'            => $category->term_id,
					);
					
					$category_query = new WP_Query( $category_args );
					
					if ( $category_query->have_posts() ) :
						$first_post = true;
						while ( $category_query->have_posts() ) :
							$category_query->the_post();
							
							if ( $first_post ) :
								// Display first post in larger format
								?>
								<div class="col-lg-6">
									<article id="post-<?php the_ID(); ?>" <?php post_class( 'post-card post-card-large' ); ?>>
										<div class="post-thumbnail">
											<?php if ( has_post_thumbnail() ) : ?>
												<a href="<?php the_permalink(); ?>">
													<?php the_post_thumbnail( 'pulsepress-featured' ); ?>
												</a>
											<?php else : ?>
												<a href="<?php the_permalink(); ?>">
													<img src="<?php echo esc_url( get_template_directory_uri() . '/assets/images/placeholder.jpg' ); ?>" alt="<?php the_title_attribute(); ?>">
												</a>
											<?php endif; ?>
										</div>
										
										<div class="post-content">
											<h3 class="entry-title"><a href="<?php the_permalink(); ?>"><?php the_title(); ?></a></h3>
											<div class="entry-meta">
												<?php
												pulsepress_posted_by();
												pulsepress_posted_on();
												?>
											</div>
											<div class="entry-summary">
												<?php the_excerpt(); ?>
											</div>
										</div>
									</article>
								</div>
								
								<div class="col-lg-6">
									<div class="row">
								<?php
								$first_post = false;
							else :
								// Display remaining posts in smaller format
								?>
								<div class="col-md-6">
									<article id="post-<?php the_ID(); ?>" <?php post_class( 'post-card post-card-small' ); ?>>
										<div class="post-thumbnail">
											<?php if ( has_post_thumbnail() ) : ?>
												<a href="<?php the_permalink(); ?>">
													<?php the_post_thumbnail( 'pulsepress-grid' ); ?>
												</a>
											<?php else : ?>
												<a href="<?php the_permalink(); ?>">
													<img src="<?php echo esc_url( get_template_directory_uri() . '/assets/images/placeholder.jpg' ); ?>" alt="<?php the_title_attribute(); ?>">
												</a>
											<?php endif; ?>
										</div>
										
										<div class="post-content">
											<h4 class="entry-title"><a href="<?php the_permalink(); ?>"><?php the_title(); ?></a></h4>
											<div class="entry-meta">
												<?php pulsepress_posted_on(); ?>
											</div>
										</div>
									</article>
								</div>
								<?php
							endif;
						endwhile;
						
						if ( ! $first_post ) {
							echo '</div></div>';
						}
						
						wp_reset_postdata();
					endif;
					?>
				</div>
			</div>
		</section>
		<?php
	endforeach;
	?>
	
	<!-- Newsletter Section -->
	<section class="newsletter-section">
		<div class="container">
			<div class="newsletter-content">
				<h2><?php esc_html_e( 'Subscribe to Our Newsletter', 'pulsepress' ); ?></h2>
				<p><?php esc_html_e( 'Get the latest news and updates delivered straight to your inbox.', 'pulsepress' ); ?></p>
				<form class="newsletter-form">
					<input type="email" placeholder="<?php esc_attr_e( 'Your Email Address', 'pulsepress' ); ?>" required>
					<button type="submit"><?php esc_html_e( 'Subscribe', 'pulsepress' ); ?></button>
				</form>
			</div>
		</div>
	</section>
	
</main><!-- #main -->

<?php
get_footer();
