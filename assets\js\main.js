/**
 * PulsePress Theme - Main JavaScript
 * 
 * Contains all the custom JavaScript functionality for the PulsePress theme
 */

(function() {
    'use strict';

    // DOM elements
    const body = document.body;
    const menuToggle = document.querySelector('.menu-toggle');
    const mainMenu = document.querySelector('.main-menu');
    const darkModeToggle = document.querySelector('.dark-mode-toggle');
    const subMenuParents = document.querySelectorAll('.menu-item-has-children, .has-mega-menu');
    
    /**
     * Initialize the theme
     */
    function init() {
        // Performance optimizations
        setupPerformanceOptimizations();

        // Core functionality
        setupMobileMenu();
        setupDarkMode();
        setupDropdownMenus();
        setupStickyHeader();
        setupSmoothScroll();
        setupLazyLoading();
        setupSocialShare();

        // Defer non-critical functionality
        requestIdleCallback(() => {
            setupAutoRefresh();
            setupLiveUpdates();
            setupImageOptimization();
            setupPrefetching();
        });
    }

    /**
     * Performance optimizations
     */
    function setupPerformanceOptimizations() {
        // Debounce scroll events
        let scrollTimeout;
        const originalScrollHandler = window.onscroll;
        window.onscroll = function() {
            if (scrollTimeout) {
                clearTimeout(scrollTimeout);
            }
            scrollTimeout = setTimeout(() => {
                if (originalScrollHandler) originalScrollHandler();
            }, 16); // ~60fps
        };

        // Optimize resize events
        let resizeTimeout;
        window.addEventListener('resize', function() {
            if (resizeTimeout) {
                clearTimeout(resizeTimeout);
            }
            resizeTimeout = setTimeout(() => {
                // Handle resize logic here
                handleResize();
            }, 250);
        });

        // Preload critical resources
        preloadCriticalResources();
    }

    /**
     * Handle window resize
     */
    function handleResize() {
        // Update mobile menu state
        if (window.innerWidth >= 992 && mainMenu) {
            mainMenu.classList.remove('active');
            if (menuToggle) {
                menuToggle.setAttribute('aria-expanded', 'false');
            }
        }
    }

    /**
     * Preload critical resources
     */
    function preloadCriticalResources() {
        // Preload next page if on single post
        if (document.body.classList.contains('single-post')) {
            const nextLink = document.querySelector('.nav-next a');
            if (nextLink) {
                const link = document.createElement('link');
                link.rel = 'prefetch';
                link.href = nextLink.href;
                document.head.appendChild(link);
            }
        }
    }

    /**
     * Mobile menu functionality
     */
    function setupMobileMenu() {
        if (!menuToggle) return;
        
        menuToggle.addEventListener('click', function() {
            mainMenu.classList.toggle('active');
            this.setAttribute('aria-expanded', this.getAttribute('aria-expanded') === 'true' ? 'false' : 'true');
        });

        // Handle submenu toggles on mobile
        subMenuParents.forEach(item => {
            const link = item.querySelector('a');
            const submenu = item.querySelector('.sub-menu, .mega-menu');
            
            if (window.innerWidth < 992) {
                link.addEventListener('click', function(e) {
                    if (window.innerWidth < 992) {
                        e.preventDefault();
                        item.classList.toggle('active');
                    }
                });
            }
        });
    }

    /**
     * Enhanced dark mode toggle functionality
     */
    function setupDarkMode() {
        if (!darkModeToggle) return;

        // Check for saved theme preference or respect OS preference
        const savedTheme = localStorage.getItem('theme');
        const prefersDark = window.matchMedia('(prefers-color-scheme: dark)').matches;

        // Initialize theme
        if (savedTheme === 'dark' || (!savedTheme && prefersDark)) {
            setDarkMode(true);
        } else {
            setDarkMode(false);
        }

        // Listen for OS theme changes
        window.matchMedia('(prefers-color-scheme: dark)').addEventListener('change', function(e) {
            if (!localStorage.getItem('theme')) {
                setDarkMode(e.matches);
            }
        });

        // Toggle dark mode on click
        darkModeToggle.addEventListener('click', function() {
            const isDark = document.documentElement.getAttribute('data-theme') === 'dark';
            setDarkMode(!isDark);
            localStorage.setItem('theme', !isDark ? 'dark' : 'light');
        });

        // Keyboard support
        darkModeToggle.addEventListener('keydown', function(e) {
            if (e.key === 'Enter' || e.key === ' ') {
                e.preventDefault();
                this.click();
            }
        });
    }

    /**
     * Set dark mode state
     */
    function setDarkMode(isDark) {
        if (isDark) {
            document.documentElement.setAttribute('data-theme', 'dark');
            darkModeToggle.classList.add('active');
            darkModeToggle.setAttribute('aria-pressed', 'true');
            darkModeToggle.setAttribute('aria-label', 'Switch to light mode');
        } else {
            document.documentElement.setAttribute('data-theme', 'light');
            darkModeToggle.classList.remove('active');
            darkModeToggle.setAttribute('aria-pressed', 'false');
            darkModeToggle.setAttribute('aria-label', 'Switch to dark mode');
        }

        // Dispatch custom event for other scripts
        document.dispatchEvent(new CustomEvent('themeChanged', {
            detail: { theme: isDark ? 'dark' : 'light' }
        }));
    }

    /**
     * Dropdown menu functionality
     */
    function setupDropdownMenus() {
        // Add keyboard navigation for dropdown menus
        subMenuParents.forEach(item => {
            const link = item.querySelector('a');
            const submenu = item.querySelector('.sub-menu, .mega-menu');
            const submenuLinks = submenu ? submenu.querySelectorAll('a') : [];
            
            // Add ARIA attributes
            link.setAttribute('aria-haspopup', 'true');
            link.setAttribute('aria-expanded', 'false');
            
            if (submenu) {
                submenu.setAttribute('aria-label', 'Submenu');
            }
            
            // Handle keyboard navigation
            link.addEventListener('keydown', function(e) {
                if (e.key === 'Enter' || e.key === ' ' || e.key === 'ArrowDown') {
                    e.preventDefault();
                    link.setAttribute('aria-expanded', 'true');
                    item.classList.add('active');
                    
                    if (submenuLinks.length > 0) {
                        submenuLinks[0].focus();
                    }
                }
            });
            
            // Handle focus trap in submenu
            if (submenuLinks.length > 0) {
                submenuLinks[submenuLinks.length - 1].addEventListener('keydown', function(e) {
                    if (e.key === 'Tab' && !e.shiftKey) {
                        e.preventDefault();
                        link.setAttribute('aria-expanded', 'false');
                        item.classList.remove('active');
                        
                        // Find the next top-level menu item and focus it
                        const nextItem = item.nextElementSibling;
                        if (nextItem) {
                            const nextLink = nextItem.querySelector('a');
                            if (nextLink) {
                                nextLink.focus();
                            }
                        }
                    }
                });
            }
        });
    }

    /**
     * Sticky header functionality
     */
    function setupStickyHeader() {
        const header = document.querySelector('.site-header');
        if (!header) return;
        
        let lastScrollTop = 0;
        const scrollThreshold = 50;
        
        window.addEventListener('scroll', function() {
            const scrollTop = window.pageYOffset || document.documentElement.scrollTop;
            
            if (scrollTop > scrollThreshold) {
                header.classList.add('sticky');
                
                // Hide header when scrolling down, show when scrolling up
                if (scrollTop > lastScrollTop) {
                    header.classList.add('hide');
                } else {
                    header.classList.remove('hide');
                }
            } else {
                header.classList.remove('sticky');
            }
            
            lastScrollTop = scrollTop;
        });
    }

    /**
     * Smooth scroll functionality
     */
    function setupSmoothScroll() {
        document.querySelectorAll('a[href^="#"]:not([href="#"])').forEach(anchor => {
            anchor.addEventListener('click', function(e) {
                e.preventDefault();
                
                const targetId = this.getAttribute('href');
                const targetElement = document.querySelector(targetId);
                
                if (targetElement) {
                    targetElement.scrollIntoView({
                        behavior: 'smooth'
                    });
                    
                    // Update URL but don't add to browser history
                    history.replaceState(null, null, targetId);
                }
            });
        });
    }

    /**
     * Lazy loading for images
     */
    function setupLazyLoading() {
        if ('loading' in HTMLImageElement.prototype) {
            // Browser supports native lazy loading
            document.querySelectorAll('img[data-src]').forEach(img => {
                img.src = img.dataset.src;
                img.loading = 'lazy';
                delete img.dataset.src;
            });
        } else {
            // Fallback for browsers that don't support native lazy loading
            const lazyImages = document.querySelectorAll('img[data-src]');
            
            if (lazyImages.length === 0) return;
            
            const lazyImageObserver = new IntersectionObserver(function(entries, observer) {
                entries.forEach(function(entry) {
                    if (entry.isIntersecting) {
                        const lazyImage = entry.target;
                        lazyImage.src = lazyImage.dataset.src;
                        delete lazyImage.dataset.src;
                        lazyImageObserver.unobserve(lazyImage);
                    }
                });
            });
            
            lazyImages.forEach(function(lazyImage) {
                lazyImageObserver.observe(lazyImage);
            });
        }
    }

    /**
     * Social sharing functionality
     */
    function setupSocialShare() {
        const shareButtons = document.querySelectorAll('.social-share-button');
        
        shareButtons.forEach(button => {
            button.addEventListener('click', function(e) {
                e.preventDefault();
                
                const url = this.dataset.url || window.location.href;
                const title = this.dataset.title || document.title;
                const platform = this.dataset.platform;
                
                let shareUrl;
                
                switch (platform) {
                    case 'facebook':
                        shareUrl = `https://www.facebook.com/sharer/sharer.php?u=${encodeURIComponent(url)}`;
                        break;
                    case 'twitter':
                        shareUrl = `https://twitter.com/intent/tweet?url=${encodeURIComponent(url)}&text=${encodeURIComponent(title)}`;
                        break;
                    case 'pinterest':
                        const image = this.dataset.image || '';
                        shareUrl = `https://pinterest.com/pin/create/button/?url=${encodeURIComponent(url)}&media=${encodeURIComponent(image)}&description=${encodeURIComponent(title)}`;
                        break;
                    case 'linkedin':
                        shareUrl = `https://www.linkedin.com/sharing/share-offsite/?url=${encodeURIComponent(url)}`;
                        break;
                    case 'email':
                        shareUrl = `mailto:?subject=${encodeURIComponent(title)}&body=${encodeURIComponent(url)}`;
                        break;
                }
                
                if (shareUrl) {
                    window.open(shareUrl, '_blank', 'width=600,height=400');
                }
            });
        });
    }

    /**
     * Auto-refresh trending content
     */
    function setupAutoRefresh() {
        const trendingTicker = document.querySelector('.trending-ticker');
        if (!trendingTicker) return;

        // Refresh every 5 minutes
        setInterval(function() {
            fetch(window.location.origin + '/wp-admin/admin-ajax.php', {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/x-www-form-urlencoded',
                },
                body: 'action=refresh_trending_ticker'
            })
            .then(response => response.json())
            .then(data => {
                if (data.success && data.data.content) {
                    trendingTicker.innerHTML = data.data.content;
                }
            })
            .catch(error => console.log('Failed to refresh trending content:', error));
        }, 300000); // 5 minutes
    }

    /**
     * Live updates for comments and notifications
     */
    function setupLiveUpdates() {
        if (!document.body.classList.contains('single-post')) return;

        const postId = document.querySelector('article.post')?.id?.replace('post-', '');
        if (!postId) return;

        // Check for new comments every 30 seconds
        setInterval(function() {
            fetch(window.location.origin + '/wp-admin/admin-ajax.php', {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/x-www-form-urlencoded',
                },
                body: `action=check_new_comments&post_id=${postId}`
            })
            .then(response => response.json())
            .then(data => {
                if (data.success && data.data.has_new) {
                    showNewCommentsNotification(data.data.count);
                }
            })
            .catch(error => console.log('Failed to check for new comments:', error));
        }, 30000); // 30 seconds
    }

    /**
     * Show new comments notification
     */
    function showNewCommentsNotification(count) {
        // Remove existing notification
        const existingNotification = document.querySelector('.new-comments-notification');
        if (existingNotification) {
            existingNotification.remove();
        }

        // Create new notification
        const notification = document.createElement('div');
        notification.className = 'new-comments-notification';
        notification.innerHTML = `
            ${count} new comment${count > 1 ? 's' : ''}
            <button class="refresh-comments">Refresh</button>
        `;

        document.body.appendChild(notification);

        // Add click handler
        notification.querySelector('.refresh-comments').addEventListener('click', function() {
            window.location.reload();
        });

        // Auto-hide after 10 seconds
        setTimeout(function() {
            if (notification.parentNode) {
                notification.remove();
            }
        }, 10000);
    }

    /**
     * Image optimization
     */
    function setupImageOptimization() {
        // Convert images to WebP if supported
        if (supportsWebP()) {
            const images = document.querySelectorAll('img[src$=".jpg"], img[src$=".jpeg"], img[src$=".png"]');
            images.forEach(img => {
                const webpSrc = img.src.replace(/\.(jpg|jpeg|png)$/, '.webp');

                // Check if WebP version exists
                const testImg = new Image();
                testImg.onload = function() {
                    img.src = webpSrc;
                };
                testImg.src = webpSrc;
            });
        }
    }

    /**
     * Check WebP support
     */
    function supportsWebP() {
        const canvas = document.createElement('canvas');
        canvas.width = 1;
        canvas.height = 1;
        return canvas.toDataURL('image/webp').indexOf('data:image/webp') === 0;
    }

    /**
     * Setup prefetching for better performance
     */
    function setupPrefetching() {
        // Prefetch links on hover
        const links = document.querySelectorAll('a[href^="/"], a[href^="' + window.location.origin + '"]');

        links.forEach(link => {
            link.addEventListener('mouseenter', function() {
                if (!this.dataset.prefetched) {
                    const prefetchLink = document.createElement('link');
                    prefetchLink.rel = 'prefetch';
                    prefetchLink.href = this.href;
                    document.head.appendChild(prefetchLink);
                    this.dataset.prefetched = 'true';
                }
            });
        });
    }

    /**
     * Fallback for requestIdleCallback
     */
    if (!window.requestIdleCallback) {
        window.requestIdleCallback = function(callback) {
            return setTimeout(callback, 1);
        };
    }

    // Initialize when DOM is fully loaded
    document.addEventListener('DOMContentLoaded', init);
})();
