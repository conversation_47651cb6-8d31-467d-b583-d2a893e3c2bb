{"version": 2, "settings": {"color": {"custom": true, "customDuotone": true, "customGradient": true, "defaultGradients": false, "defaultPalette": false, "palette": [{"slug": "primary", "color": "#1e73be", "name": "Primary"}, {"slug": "secondary", "color": "#ff6b6b", "name": "Secondary"}, {"slug": "accent", "color": "#ffd166", "name": "Accent"}, {"slug": "dark", "color": "#2c3e50", "name": "Dark"}, {"slug": "light", "color": "#f8f9fa", "name": "Light"}, {"slug": "gray", "color": "#6c757d", "name": "<PERSON>"}, {"slug": "white", "color": "#ffffff", "name": "White"}, {"slug": "black", "color": "#000000", "name": "Black"}], "gradients": [{"slug": "primary-to-secondary", "gradient": "linear-gradient(135deg, var(--wp--preset--color--primary) 0%, var(--wp--preset--color--secondary) 100%)", "name": "Primary to Secondary"}, {"slug": "dark-to-light", "gradient": "linear-gradient(135deg, var(--wp--preset--color--dark) 0%, var(--wp--preset--color--light) 100%)", "name": "Dark to Light"}]}, "typography": {"customFontSize": true, "fontStyle": true, "fontWeight": true, "letterSpacing": true, "lineHeight": true, "textDecoration": true, "textTransform": true, "dropCap": false, "fontSizes": [{"slug": "small", "size": "0.875rem", "name": "Small"}, {"slug": "normal", "size": "1rem", "name": "Normal"}, {"slug": "medium", "size": "1.125rem", "name": "Medium"}, {"slug": "large", "size": "1.25rem", "name": "Large"}, {"slug": "x-large", "size": "1.5rem", "name": "Extra Large"}, {"slug": "xx-large", "size": "2rem", "name": "Extra Extra Large"}], "fontFamilies": [{"fontFamily": "'Roboto', sans-serif", "slug": "body", "name": "Body Font (Roboto)"}, {"fontFamily": "'Montserrat', sans-serif", "slug": "heading", "name": "Heading Font (Montserrat)"}, {"fontFamily": "Georgia, serif", "slug": "serif", "name": "<PERSON><PERSON>"}, {"fontFamily": "'Courier New', monospace", "slug": "monospace", "name": "Monospace"}]}, "spacing": {"blockGap": true, "margin": true, "padding": true, "units": ["px", "em", "rem", "vh", "vw", "%"], "spacingSizes": [{"slug": "xs", "size": "0.25rem", "name": "Extra Small"}, {"slug": "sm", "size": "0.5rem", "name": "Small"}, {"slug": "md", "size": "1rem", "name": "Medium"}, {"slug": "lg", "size": "1.5rem", "name": "Large"}, {"slug": "xl", "size": "3rem", "name": "Extra Large"}]}, "border": {"color": true, "radius": true, "style": true, "width": true}, "layout": {"contentSize": "720px", "wideSize": "1140px"}, "blocks": {"core/button": {"color": {"palette": [{"slug": "primary", "color": "#1e73be", "name": "Primary"}, {"slug": "secondary", "color": "#ff6b6b", "name": "Secondary"}]}}, "core/heading": {"typography": {"fontFamilies": [{"fontFamily": "'Montserrat', sans-serif", "slug": "heading", "name": "Heading Font"}]}}, "core/paragraph": {"typography": {"fontFamilies": [{"fontFamily": "'Roboto', sans-serif", "slug": "body", "name": "Body Font"}]}}}}, "styles": {"color": {"background": "var(--wp--preset--color--white)", "text": "var(--wp--preset--color--dark)"}, "typography": {"fontFamily": "var(--wp--preset--font-family--body)", "fontSize": "var(--wp--preset--font-size--normal)", "lineHeight": "1.6"}, "spacing": {"blockGap": "var(--wp--preset--spacing--md)"}, "elements": {"link": {"color": {"text": "var(--wp--preset--color--primary)"}, ":hover": {"color": {"text": "var(--wp--preset--color--secondary)"}}}, "h1": {"typography": {"fontFamily": "var(--wp--preset--font-family--heading)", "fontSize": "2.5rem", "fontWeight": "700", "lineHeight": "1.2"}, "spacing": {"margin": {"bottom": "var(--wp--preset--spacing--lg)"}}}, "h2": {"typography": {"fontFamily": "var(--wp--preset--font-family--heading)", "fontSize": "2rem", "fontWeight": "600", "lineHeight": "1.3"}, "spacing": {"margin": {"bottom": "var(--wp--preset--spacing--md)"}}}, "h3": {"typography": {"fontFamily": "var(--wp--preset--font-family--heading)", "fontSize": "1.75rem", "fontWeight": "600", "lineHeight": "1.3"}}, "h4": {"typography": {"fontFamily": "var(--wp--preset--font-family--heading)", "fontSize": "1.5rem", "fontWeight": "500"}}, "h5": {"typography": {"fontFamily": "var(--wp--preset--font-family--heading)", "fontSize": "1.25rem", "fontWeight": "500"}}, "h6": {"typography": {"fontFamily": "var(--wp--preset--font-family--heading)", "fontSize": "1rem", "fontWeight": "500"}}}, "blocks": {"core/button": {"color": {"background": "var(--wp--preset--color--primary)", "text": "var(--wp--preset--color--white)"}, "typography": {"fontWeight": "500"}, "border": {"radius": "0.25rem"}, "spacing": {"padding": {"top": "0.75rem", "bottom": "0.75rem", "left": "1.5rem", "right": "1.5rem"}}, ":hover": {"color": {"background": "var(--wp--preset--color--secondary)"}}}, "core/quote": {"border": {"color": "var(--wp--preset--color--primary)", "style": "solid", "width": "0 0 0 4px"}, "spacing": {"padding": {"left": "var(--wp--preset--spacing--lg)"}}, "typography": {"fontStyle": "italic"}}, "core/pullquote": {"border": {"color": "var(--wp--preset--color--primary)", "style": "solid", "width": "4px 0"}, "spacing": {"padding": {"top": "var(--wp--preset--spacing--lg)", "bottom": "var(--wp--preset--spacing--lg)"}}}, "core/separator": {"color": {"text": "var(--wp--preset--color--gray)"}}, "core/code": {"color": {"background": "var(--wp--preset--color--light)", "text": "var(--wp--preset--color--dark)"}, "typography": {"fontFamily": "var(--wp--preset--font-family--monospace)"}, "spacing": {"padding": {"top": "var(--wp--preset--spacing--sm)", "bottom": "var(--wp--preset--spacing--sm)", "left": "var(--wp--preset--spacing--sm)", "right": "var(--wp--preset--spacing--sm)"}}, "border": {"radius": "0.25rem"}}, "core/preformatted": {"color": {"background": "var(--wp--preset--color--dark)", "text": "var(--wp--preset--color--light)"}, "typography": {"fontFamily": "var(--wp--preset--font-family--monospace)"}, "spacing": {"padding": "var(--wp--preset--spacing--md)"}, "border": {"radius": "0.25rem"}}, "core/table": {"border": {"color": "var(--wp--preset--color--gray)"}}}}, "templateParts": [{"name": "header", "title": "Header", "area": "header"}, {"name": "footer", "title": "Footer", "area": "footer"}], "customTemplates": [{"name": "blank", "title": "Blank", "postTypes": ["page", "post"]}, {"name": "full-width", "title": "Full Width", "postTypes": ["page"]}]}