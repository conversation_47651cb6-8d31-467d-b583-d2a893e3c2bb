<?php
/**
 * Social Media Integration for PulsePress Theme
 *
 * @package PulsePress
 */

/**
 * Register the social media settings page in the admin menu.
 */
function pulsepress_social_media_menu() {
	add_submenu_page(
		'themes.php',
		esc_html__( 'Social Media Integration', 'pulsepress' ),
		esc_html__( 'Social Media', 'pulsepress' ),
		'manage_options',
		'pulsepress-social-media',
		'pulsepress_social_media_page'
	);
}
add_action( 'admin_menu', 'pulsepress_social_media_menu' );

/**
 * Register social media settings.
 */
function pulsepress_social_media_settings() {
	// Register settings
	register_setting( 'pulsepress_social_media_group', 'pulsepress_facebook_app_id' );
	register_setting( 'pulsepress_social_media_group', 'pulsepress_facebook_app_secret' );
	register_setting( 'pulsepress_social_media_group', 'pulsepress_facebook_access_token' );
	
	register_setting( 'pulsepress_social_media_group', 'pulsepress_instagram_app_id' );
	register_setting( 'pulsepress_social_media_group', 'pulsepress_instagram_app_secret' );
	register_setting( 'pulsepress_social_media_group', 'pulsepress_instagram_access_token' );
	
	register_setting( 'pulsepress_social_media_group', 'pulsepress_pinterest_app_id' );
	register_setting( 'pulsepress_social_media_group', 'pulsepress_pinterest_app_secret' );
	register_setting( 'pulsepress_social_media_group', 'pulsepress_pinterest_access_token' );
	
	register_setting( 'pulsepress_social_media_group', 'pulsepress_auto_share_facebook' );
	register_setting( 'pulsepress_social_media_group', 'pulsepress_auto_share_instagram' );
	register_setting( 'pulsepress_social_media_group', 'pulsepress_auto_share_pinterest' );
}
add_action( 'admin_init', 'pulsepress_social_media_settings' );

/**
 * Render the social media settings page.
 */
function pulsepress_social_media_page() {
	?>
	<div class="wrap">
		<h1><?php esc_html_e( 'Social Media Integration', 'pulsepress' ); ?></h1>
		
		<p><?php esc_html_e( 'Configure your social media API credentials to enable automatic post sharing.', 'pulsepress' ); ?></p>
		
		<form method="post" action="options.php">
			<?php settings_fields( 'pulsepress_social_media_group' ); ?>
			<?php do_settings_sections( 'pulsepress_social_media_group' ); ?>
			
			<h2><?php esc_html_e( 'Facebook Integration', 'pulsepress' ); ?></h2>
			<p>
				<?php esc_html_e( 'To obtain your Facebook API credentials:', 'pulsepress' ); ?>
				<ol>
					<li><?php esc_html_e( 'Go to', 'pulsepress' ); ?> <a href="https://developers.facebook.com/" target="_blank">https://developers.facebook.com/</a></li>
					<li><?php esc_html_e( 'Create a new app', 'pulsepress' ); ?></li>
					<li><?php esc_html_e( 'Add the Facebook Login product', 'pulsepress' ); ?></li>
					<li><?php esc_html_e( 'Set up OAuth redirect to:', 'pulsepress' ); ?> <code><?php echo esc_url( admin_url( 'themes.php?page=pulsepress-social-media' ) ); ?></code></li>
					<li><?php esc_html_e( 'Copy your App ID and App Secret below', 'pulsepress' ); ?></li>
				</ol>
			</p>
			
			<table class="form-table">
				<tr valign="top">
					<th scope="row"><?php esc_html_e( 'App ID', 'pulsepress' ); ?></th>
					<td><input type="text" name="pulsepress_facebook_app_id" value="<?php echo esc_attr( get_option( 'pulsepress_facebook_app_id' ) ); ?>" class="regular-text" /></td>
				</tr>
				<tr valign="top">
					<th scope="row"><?php esc_html_e( 'App Secret', 'pulsepress' ); ?></th>
					<td><input type="password" name="pulsepress_facebook_app_secret" value="<?php echo esc_attr( get_option( 'pulsepress_facebook_app_secret' ) ); ?>" class="regular-text" /></td>
				</tr>
				<tr valign="top">
					<th scope="row"><?php esc_html_e( 'Access Token', 'pulsepress' ); ?></th>
					<td>
						<input type="text" name="pulsepress_facebook_access_token" value="<?php echo esc_attr( get_option( 'pulsepress_facebook_access_token' ) ); ?>" class="regular-text" />
						<?php if ( get_option( 'pulsepress_facebook_app_id' ) && get_option( 'pulsepress_facebook_app_secret' ) ) : ?>
							<p><a href="<?php echo esc_url( pulsepress_get_facebook_auth_url() ); ?>" class="button"><?php esc_html_e( 'Get Access Token', 'pulsepress' ); ?></a></p>
						<?php endif; ?>
					</td>
				</tr>
				<tr valign="top">
					<th scope="row"><?php esc_html_e( 'Auto-Share Posts', 'pulsepress' ); ?></th>
					<td>
						<label>
							<input type="checkbox" name="pulsepress_auto_share_facebook" value="1" <?php checked( get_option( 'pulsepress_auto_share_facebook' ), 1 ); ?> />
							<?php esc_html_e( 'Automatically share new posts to Facebook', 'pulsepress' ); ?>
						</label>
					</td>
				</tr>
			</table>
			
			<h2><?php esc_html_e( 'Instagram Integration', 'pulsepress' ); ?></h2>
			<p>
				<?php esc_html_e( 'To obtain your Instagram API credentials:', 'pulsepress' ); ?>
				<ol>
					<li><?php esc_html_e( 'Go to', 'pulsepress' ); ?> <a href="https://developers.facebook.com/" target="_blank">https://developers.facebook.com/</a></li>
					<li><?php esc_html_e( 'Create a new app or use your existing Facebook app', 'pulsepress' ); ?></li>
					<li><?php esc_html_e( 'Add the Instagram Basic Display product', 'pulsepress' ); ?></li>
					<li><?php esc_html_e( 'Set up OAuth redirect to:', 'pulsepress' ); ?> <code><?php echo esc_url( admin_url( 'themes.php?page=pulsepress-social-media' ) ); ?></code></li>
					<li><?php esc_html_e( 'Copy your App ID and App Secret below', 'pulsepress' ); ?></li>
				</ol>
			</p>
			
			<table class="form-table">
				<tr valign="top">
					<th scope="row"><?php esc_html_e( 'App ID', 'pulsepress' ); ?></th>
					<td><input type="text" name="pulsepress_instagram_app_id" value="<?php echo esc_attr( get_option( 'pulsepress_instagram_app_id' ) ); ?>" class="regular-text" /></td>
				</tr>
				<tr valign="top">
					<th scope="row"><?php esc_html_e( 'App Secret', 'pulsepress' ); ?></th>
					<td><input type="password" name="pulsepress_instagram_app_secret" value="<?php echo esc_attr( get_option( 'pulsepress_instagram_app_secret' ) ); ?>" class="regular-text" /></td>
				</tr>
				<tr valign="top">
					<th scope="row"><?php esc_html_e( 'Access Token', 'pulsepress' ); ?></th>
					<td>
						<input type="text" name="pulsepress_instagram_access_token" value="<?php echo esc_attr( get_option( 'pulsepress_instagram_access_token' ) ); ?>" class="regular-text" />
						<?php if ( get_option( 'pulsepress_instagram_app_id' ) && get_option( 'pulsepress_instagram_app_secret' ) ) : ?>
							<p><a href="<?php echo esc_url( pulsepress_get_instagram_auth_url() ); ?>" class="button"><?php esc_html_e( 'Get Access Token', 'pulsepress' ); ?></a></p>
						<?php endif; ?>
					</td>
				</tr>
				<tr valign="top">
					<th scope="row"><?php esc_html_e( 'Auto-Share Posts', 'pulsepress' ); ?></th>
					<td>
						<label>
							<input type="checkbox" name="pulsepress_auto_share_instagram" value="1" <?php checked( get_option( 'pulsepress_auto_share_instagram' ), 1 ); ?> />
							<?php esc_html_e( 'Automatically share new posts to Instagram', 'pulsepress' ); ?>
						</label>
					</td>
				</tr>
			</table>
			
			<h2><?php esc_html_e( 'Pinterest Integration', 'pulsepress' ); ?></h2>
			<p>
				<?php esc_html_e( 'To obtain your Pinterest API credentials:', 'pulsepress' ); ?>
				<ol>
					<li><?php esc_html_e( 'Go to', 'pulsepress' ); ?> <a href="https://developers.pinterest.com/" target="_blank">https://developers.pinterest.com/</a></li>
					<li><?php esc_html_e( 'Create a new app', 'pulsepress' ); ?></li>
					<li><?php esc_html_e( 'Set up OAuth redirect to:', 'pulsepress' ); ?> <code><?php echo esc_url( admin_url( 'themes.php?page=pulsepress-social-media' ) ); ?></code></li>
					<li><?php esc_html_e( 'Copy your App ID and App Secret below', 'pulsepress' ); ?></li>
				</ol>
			</p>
			
			<table class="form-table">
				<tr valign="top">
					<th scope="row"><?php esc_html_e( 'App ID', 'pulsepress' ); ?></th>
					<td><input type="text" name="pulsepress_pinterest_app_id" value="<?php echo esc_attr( get_option( 'pulsepress_pinterest_app_id' ) ); ?>" class="regular-text" /></td>
				</tr>
				<tr valign="top">
					<th scope="row"><?php esc_html_e( 'App Secret', 'pulsepress' ); ?></th>
					<td><input type="password" name="pulsepress_pinterest_app_secret" value="<?php echo esc_attr( get_option( 'pulsepress_pinterest_app_secret' ) ); ?>" class="regular-text" /></td>
				</tr>
				<tr valign="top">
					<th scope="row"><?php esc_html_e( 'Access Token', 'pulsepress' ); ?></th>
					<td>
						<input type="text" name="pulsepress_pinterest_access_token" value="<?php echo esc_attr( get_option( 'pulsepress_pinterest_access_token' ) ); ?>" class="regular-text" />
						<?php if ( get_option( 'pulsepress_pinterest_app_id' ) && get_option( 'pulsepress_pinterest_app_secret' ) ) : ?>
							<p><a href="<?php echo esc_url( pulsepress_get_pinterest_auth_url() ); ?>" class="button"><?php esc_html_e( 'Get Access Token', 'pulsepress' ); ?></a></p>
						<?php endif; ?>
					</td>
				</tr>
				<tr valign="top">
					<th scope="row"><?php esc_html_e( 'Auto-Share Posts', 'pulsepress' ); ?></th>
					<td>
						<label>
							<input type="checkbox" name="pulsepress_auto_share_pinterest" value="1" <?php checked( get_option( 'pulsepress_auto_share_pinterest' ), 1 ); ?> />
							<?php esc_html_e( 'Automatically share new posts to Pinterest', 'pulsepress' ); ?>
						</label>
					</td>
				</tr>
			</table>
			
			<?php submit_button(); ?>
		</form>
	</div>
	<?php
}

/**
 * Get Facebook authentication URL.
 *
 * @return string Authentication URL.
 */
function pulsepress_get_facebook_auth_url() {
	$app_id = get_option( 'pulsepress_facebook_app_id' );
	$redirect_uri = admin_url( 'themes.php?page=pulsepress-social-media' );
	$scope = 'pages_manage_posts,pages_read_engagement';
	
	return 'https://www.facebook.com/v12.0/dialog/oauth?client_id=' . $app_id . '&redirect_uri=' . urlencode( $redirect_uri ) . '&scope=' . $scope;
}

/**
 * Get Instagram authentication URL.
 *
 * @return string Authentication URL.
 */
function pulsepress_get_instagram_auth_url() {
	$app_id = get_option( 'pulsepress_instagram_app_id' );
	$redirect_uri = admin_url( 'themes.php?page=pulsepress-social-media' );
	$scope = 'user_profile,user_media';
	
	return 'https://api.instagram.com/oauth/authorize?client_id=' . $app_id . '&redirect_uri=' . urlencode( $redirect_uri ) . '&scope=' . $scope . '&response_type=code';
}

/**
 * Get Pinterest authentication URL.
 *
 * @return string Authentication URL.
 */
function pulsepress_get_pinterest_auth_url() {
	$app_id = get_option( 'pulsepress_pinterest_app_id' );
	$redirect_uri = admin_url( 'themes.php?page=pulsepress-social-media' );
	$scope = 'boards:read,pins:read,pins:write';
	
	return 'https://www.pinterest.com/oauth/?client_id=' . $app_id . '&redirect_uri=' . urlencode( $redirect_uri ) . '&scope=' . $scope . '&response_type=code';
}

/**
 * Handle OAuth callbacks.
 */
function pulsepress_handle_oauth_callbacks() {
	if ( ! isset( $_GET['page'] ) || 'pulsepress-social-media' !== $_GET['page'] ) {
		return;
	}
	
	// Handle Facebook OAuth callback
	if ( isset( $_GET['code'] ) && isset( $_GET['state'] ) ) {
		// Process Facebook OAuth response
		// This is a simplified example - in a real implementation, you would exchange the code for an access token
		add_settings_error( 'pulsepress_social_media_group', 'facebook_auth', esc_html__( 'Facebook authentication received. Please complete the process by exchanging the code for an access token.', 'pulsepress' ), 'info' );
	}
	
	// Handle Instagram OAuth callback
	if ( isset( $_GET['code'] ) && ! isset( $_GET['state'] ) ) {
		// Process Instagram OAuth response
		// This is a simplified example - in a real implementation, you would exchange the code for an access token
		add_settings_error( 'pulsepress_social_media_group', 'instagram_auth', esc_html__( 'Instagram authentication received. Please complete the process by exchanging the code for an access token.', 'pulsepress' ), 'info' );
	}
}
add_action( 'admin_init', 'pulsepress_handle_oauth_callbacks' );

/**
 * Share post to social media when published.
 *
 * @param int     $post_id Post ID.
 * @param WP_Post $post    Post object.
 */
function pulsepress_share_post( $post_id, $post ) {
	// Only share posts, not pages or other post types
	if ( 'post' !== $post->post_type ) {
		return;
	}
	
	// Only share published posts
	if ( 'publish' !== $post->post_status ) {
		return;
	}
	
	// Don't share if this is an update to an existing post
	if ( get_post_meta( $post_id, 'pulsepress_shared_to_social', true ) ) {
		return;
	}
	
	// Share to Facebook
	if ( get_option( 'pulsepress_auto_share_facebook' ) && get_option( 'pulsepress_facebook_access_token' ) ) {
		pulsepress_share_to_facebook( $post_id );
	}
	
	// Share to Instagram
	if ( get_option( 'pulsepress_auto_share_instagram' ) && get_option( 'pulsepress_instagram_access_token' ) ) {
		pulsepress_share_to_instagram( $post_id );
	}
	
	// Share to Pinterest
	if ( get_option( 'pulsepress_auto_share_pinterest' ) && get_option( 'pulsepress_pinterest_access_token' ) ) {
		pulsepress_share_to_pinterest( $post_id );
	}
	
	// Mark post as shared
	update_post_meta( $post_id, 'pulsepress_shared_to_social', true );
}
add_action( 'wp_insert_post', 'pulsepress_share_post', 10, 2 );

/**
 * Share post to Facebook.
 *
 * @param int $post_id Post ID.
 */
function pulsepress_share_to_facebook( $post_id ) {
	// This is a placeholder function
	// In a real implementation, you would use the Facebook Graph API to share the post
	
	// Get post data
	$post = get_post( $post_id );
	$title = $post->post_title;
	$excerpt = wp_strip_all_tags( get_the_excerpt( $post_id ) );
	$permalink = get_permalink( $post_id );
	$image = get_the_post_thumbnail_url( $post_id, 'full' );
	
	// Log the sharing attempt
	error_log( 'Shared to Facebook: ' . $title );
}

/**
 * Share post to Instagram.
 *
 * @param int $post_id Post ID.
 */
function pulsepress_share_to_instagram( $post_id ) {
	// This is a placeholder function
	// In a real implementation, you would use the Instagram API to share the post
	
	// Get post data
	$post = get_post( $post_id );
	$title = $post->post_title;
	$excerpt = wp_strip_all_tags( get_the_excerpt( $post_id ) );
	$permalink = get_permalink( $post_id );
	$image = get_the_post_thumbnail_url( $post_id, 'full' );
	
	// Log the sharing attempt
	error_log( 'Shared to Instagram: ' . $title );
}

/**
 * Share post to Pinterest.
 *
 * @param int $post_id Post ID.
 */
function pulsepress_share_to_pinterest( $post_id ) {
	// This is a placeholder function
	// In a real implementation, you would use the Pinterest API to share the post
	
	// Get post data
	$post = get_post( $post_id );
	$title = $post->post_title;
	$excerpt = wp_strip_all_tags( get_the_excerpt( $post_id ) );
	$permalink = get_permalink( $post_id );
	$image = get_the_post_thumbnail_url( $post_id, 'full' );
	
	// Log the sharing attempt
	error_log( 'Shared to Pinterest: ' . $title );
}
