/**
 * PulsePress Custom Blocks Styles
 */

/* ===============================
   Editor's Picks Block
=============================== */
.wp-block-pulsepress-editors-picks {
    margin: 2rem 0;
}

.wp-block-pulsepress-editors-picks .block-title {
    font-size: 1.75rem;
    font-weight: 700;
    margin-bottom: 1.5rem;
    color: var(--primary-color);
    border-bottom: 3px solid var(--primary-color);
    padding-bottom: 0.5rem;
    position: relative;
}

.wp-block-pulsepress-editors-picks .block-title::after {
    content: '';
    position: absolute;
    bottom: -3px;
    left: 0;
    width: 60px;
    height: 3px;
    background: var(--secondary-color);
}

.editors-picks-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(280px, 1fr));
    gap: 1.5rem;
}

.editors-pick-item {
    background: white;
    border-radius: var(--border-radius);
    overflow: hidden;
    box-shadow: 0 2px 10px rgba(0, 0, 0, 0.1);
    transition: transform 0.3s ease, box-shadow 0.3s ease;
}

.editors-pick-item:hover {
    transform: translateY(-5px);
    box-shadow: 0 8px 25px rgba(0, 0, 0, 0.15);
}

.editors-pick-item .post-thumbnail {
    position: relative;
    overflow: hidden;
}

.editors-pick-item .post-thumbnail img {
    width: 100%;
    height: 200px;
    object-fit: cover;
    transition: transform 0.3s ease;
}

.editors-pick-item:hover .post-thumbnail img {
    transform: scale(1.05);
}

.editors-pick-item .post-content {
    padding: 1.25rem;
}

.editors-pick-item .post-title {
    font-size: 1.125rem;
    font-weight: 600;
    margin-bottom: 0.75rem;
    line-height: 1.4;
}

.editors-pick-item .post-title a {
    color: var(--dark-color);
    text-decoration: none;
    transition: color 0.3s ease;
}

.editors-pick-item .post-title a:hover {
    color: var(--primary-color);
}

.editors-pick-item .post-excerpt {
    color: var(--gray-color);
    font-size: 0.875rem;
    line-height: 1.5;
    margin-bottom: 1rem;
}

.editors-pick-item .post-meta {
    display: flex;
    justify-content: space-between;
    align-items: center;
    font-size: 0.75rem;
    color: var(--gray-color);
}

.editors-pick-item .post-author a {
    color: var(--primary-color);
    text-decoration: none;
}

/* ===============================
   Weekly Top Stories Block
=============================== */
.wp-block-pulsepress-weekly-top-stories {
    margin: 2rem 0;
    background: var(--light-color);
    padding: 2rem;
    border-radius: var(--border-radius);
}

.wp-block-pulsepress-weekly-top-stories .block-title {
    font-size: 1.5rem;
    font-weight: 700;
    margin-bottom: 1.5rem;
    color: var(--dark-color);
    text-align: center;
}

.top-stories-list {
    display: flex;
    flex-direction: column;
    gap: 1rem;
}

.top-story-item {
    display: flex;
    align-items: center;
    gap: 1rem;
    padding: 1rem;
    background: white;
    border-radius: var(--border-radius);
    transition: transform 0.3s ease;
}

.top-story-item:hover {
    transform: translateX(5px);
}

.story-number {
    display: flex;
    align-items: center;
    justify-content: center;
    width: 40px;
    height: 40px;
    background: var(--primary-color);
    color: white;
    font-weight: 700;
    font-size: 1.125rem;
    border-radius: 50%;
    flex-shrink: 0;
}

.story-content {
    flex: 1;
}

.story-title {
    font-size: 1rem;
    font-weight: 600;
    margin-bottom: 0.5rem;
    line-height: 1.3;
}

.story-title a {
    color: var(--dark-color);
    text-decoration: none;
    transition: color 0.3s ease;
}

.story-title a:hover {
    color: var(--primary-color);
}

.story-meta {
    display: flex;
    gap: 1rem;
    font-size: 0.75rem;
    color: var(--gray-color);
}

.story-views {
    font-weight: 500;
}

/* ===============================
   Featured Category Block
=============================== */
.wp-block-pulsepress-featured-category {
    margin: 2rem 0;
}

.wp-block-pulsepress-featured-category .block-title {
    font-size: 1.5rem;
    font-weight: 700;
    margin-bottom: 1.5rem;
    text-align: center;
}

.wp-block-pulsepress-featured-category .block-title a {
    color: var(--primary-color);
    text-decoration: none;
    transition: color 0.3s ease;
}

.wp-block-pulsepress-featured-category .block-title a:hover {
    color: var(--secondary-color);
}

.featured-category-posts.layout-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
    gap: 1.5rem;
}

.featured-category-posts.layout-list {
    display: flex;
    flex-direction: column;
    gap: 1rem;
}

.featured-post-item {
    background: white;
    border-radius: var(--border-radius);
    overflow: hidden;
    box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
    transition: transform 0.3s ease;
}

.layout-grid .featured-post-item:hover {
    transform: translateY(-3px);
}

.layout-list .featured-post-item {
    display: flex;
    align-items: center;
    gap: 1rem;
    padding: 1rem;
}

.layout-list .featured-post-item .post-thumbnail {
    flex-shrink: 0;
    width: 120px;
    height: 80px;
}

.layout-list .featured-post-item .post-thumbnail img {
    width: 100%;
    height: 100%;
    object-fit: cover;
    border-radius: var(--border-radius);
}

.layout-grid .featured-post-item .post-thumbnail img {
    width: 100%;
    height: 180px;
    object-fit: cover;
}

.layout-grid .featured-post-item .post-content {
    padding: 1.25rem;
}

.featured-post-item .post-title {
    font-size: 1rem;
    font-weight: 600;
    margin-bottom: 0.5rem;
    line-height: 1.4;
}

.featured-post-item .post-title a {
    color: var(--dark-color);
    text-decoration: none;
    transition: color 0.3s ease;
}

.featured-post-item .post-title a:hover {
    color: var(--primary-color);
}

.featured-post-item .post-excerpt {
    color: var(--gray-color);
    font-size: 0.875rem;
    line-height: 1.5;
    margin-bottom: 0.75rem;
}

.featured-post-item .post-meta {
    font-size: 0.75rem;
    color: var(--gray-color);
}

.view-all-link {
    text-align: center;
    margin-top: 2rem;
}

.view-all-link .btn {
    display: inline-block;
    padding: 0.75rem 2rem;
    background: var(--primary-color);
    color: white;
    text-decoration: none;
    border-radius: var(--border-radius);
    font-weight: 500;
    transition: background 0.3s ease;
}

.view-all-link .btn:hover {
    background: var(--secondary-color);
}

/* ===============================
   Breaking News Block
=============================== */
.wp-block-pulsepress-breaking-news {
    margin: 1rem 0;
    background: var(--primary-color);
    color: white;
    border-radius: var(--border-radius);
    overflow: hidden;
    animation: slideIn 0.5s ease-out;
}

.wp-block-pulsepress-breaking-news.urgent {
    background: var(--danger-color);
    animation: pulse 2s infinite;
}

@keyframes slideIn {
    from {
        transform: translateX(-100%);
        opacity: 0;
    }
    to {
        transform: translateX(0);
        opacity: 1;
    }
}

@keyframes pulse {
    0%, 100% {
        opacity: 1;
    }
    50% {
        opacity: 0.8;
    }
}

.breaking-news-content {
    display: flex;
    align-items: center;
    gap: 1rem;
    padding: 1rem 1.5rem;
}

.breaking-news-label {
    background: rgba(255, 255, 255, 0.2);
    padding: 0.25rem 0.75rem;
    border-radius: var(--border-radius);
    font-weight: 700;
    font-size: 0.875rem;
    text-transform: uppercase;
    letter-spacing: 0.5px;
    flex-shrink: 0;
}

.breaking-news-text {
    flex: 1;
    font-weight: 500;
}

.breaking-news-text a {
    color: white;
    text-decoration: none;
    transition: opacity 0.3s ease;
}

.breaking-news-text a:hover {
    opacity: 0.8;
}

/* ===============================
   Dark Mode Support
=============================== */
[data-theme="dark"] .editors-pick-item,
[data-theme="dark"] .top-story-item,
[data-theme="dark"] .featured-post-item {
    background: var(--card-bg);
    color: var(--text-color);
}

[data-theme="dark"] .wp-block-pulsepress-weekly-top-stories {
    background: var(--card-bg);
}

[data-theme="dark"] .editors-pick-item .post-title a,
[data-theme="dark"] .story-title a,
[data-theme="dark"] .featured-post-item .post-title a {
    color: var(--text-color);
}

/* ===============================
   Responsive Design
=============================== */
@media (max-width: 768px) {
    .editors-picks-grid {
        grid-template-columns: 1fr;
    }
    
    .featured-category-posts.layout-grid {
        grid-template-columns: 1fr;
    }
    
    .layout-list .featured-post-item {
        flex-direction: column;
        text-align: center;
    }
    
    .layout-list .featured-post-item .post-thumbnail {
        width: 100%;
        height: 150px;
    }
    
    .breaking-news-content {
        flex-direction: column;
        text-align: center;
        gap: 0.5rem;
    }
    
    .wp-block-pulsepress-weekly-top-stories {
        padding: 1rem;
    }
}

@media (max-width: 480px) {
    .top-story-item {
        padding: 0.75rem;
    }
    
    .story-number {
        width: 30px;
        height: 30px;
        font-size: 1rem;
    }
    
    .story-title {
        font-size: 0.875rem;
    }
    
    .breaking-news-content {
        padding: 0.75rem 1rem;
    }
}

/* ===============================
   Print Styles
=============================== */
@media print {
    .wp-block-pulsepress-breaking-news {
        background: transparent !important;
        color: black !important;
        border: 2px solid black;
    }
    
    .breaking-news-label {
        background: black !important;
        color: white !important;
    }
}
