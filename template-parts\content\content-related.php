<?php
/**
 * Template part for displaying related posts
 *
 * @link https://developer.wordpress.org/themes/basics/template-hierarchy/
 *
 * @package PulsePress
 */

// Get current post categories
$categories = get_the_category();
$category_ids = array();

if ( $categories ) {
	foreach ( $categories as $category ) {
		$category_ids[] = $category->term_id;
	}
}

// Get related posts
$related_args = array(
	'post_type'      => 'post',
	'posts_per_page' => 3,
	'post__not_in'   => array( get_the_ID() ),
	'category__in'   => $category_ids,
);

$related_query = new WP_Query( $related_args );

if ( $related_query->have_posts() ) :
	?>
	<div class="related-posts">
		<h3 class="related-title"><?php esc_html_e( 'Related Posts', 'pulsepress' ); ?></h3>
		
		<div class="row">
			<?php
			while ( $related_query->have_posts() ) :
				$related_query->the_post();
				?>
				<div class="col-md-4">
					<article id="post-<?php the_ID(); ?>" <?php post_class( 'post-card related-post' ); ?>>
						<div class="post-thumbnail">
							<?php if ( has_post_thumbnail() ) : ?>
								<a href="<?php the_permalink(); ?>">
									<?php the_post_thumbnail( 'pulsepress-grid' ); ?>
								</a>
							<?php else : ?>
								<a href="<?php the_permalink(); ?>">
									<img src="<?php echo esc_url( get_template_directory_uri() . '/assets/images/placeholder.jpg' ); ?>" alt="<?php the_title_attribute(); ?>">
								</a>
							<?php endif; ?>
							<div class="post-category">
								<?php
								$categories = get_the_category();
								if ( ! empty( $categories ) ) {
									echo '<a href="' . esc_url( get_category_link( $categories[0]->term_id ) ) . '">' . esc_html( $categories[0]->name ) . '</a>';
								}
								?>
							</div>
						</div>
						
						<div class="post-content">
							<h3 class="entry-title"><a href="<?php the_permalink(); ?>"><?php the_title(); ?></a></h3>
							<div class="entry-meta">
								<?php pulsepress_posted_on(); ?>
							</div>
						</div>
					</article>
				</div>
				<?php
			endwhile;
			?>
		</div>
	</div>
	<?php
endif;
wp_reset_postdata();
