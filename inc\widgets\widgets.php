<?php
/**
 * Custom widgets for PulsePress theme
 *
 * @package PulsePress
 */

/**
 * Register custom widgets.
 */
function pulsepress_register_widgets() {
	register_widget( 'PulsePress_Recent_Posts_Widget' );
	register_widget( 'PulsePress_Popular_Posts_Widget' );
	register_widget( 'PulsePress_Social_Widget' );
	register_widget( 'PulsePress_Newsletter_Widget' );
}
add_action( 'widgets_init', 'pulsepress_register_widgets' );

/**
 * Recent Posts Widget with Thumbnails
 */
class PulsePress_Recent_Posts_Widget extends WP_Widget {

	/**
	 * Register widget with WordPress.
	 */
	public function __construct() {
		parent::__construct(
			'pulsepress_recent_posts',
			esc_html__( 'PulsePress: Recent Posts', 'pulsepress' ),
			array(
				'description' => esc_html__( 'Display recent posts with thumbnails.', 'pulsepress' ),
			)
		);
	}

	/**
	 * Front-end display of widget.
	 *
	 * @param array $args     Widget arguments.
	 * @param array $instance Saved values from database.
	 */
	public function widget( $args, $instance ) {
		$title = ! empty( $instance['title'] ) ? $instance['title'] : esc_html__( 'Recent Posts', 'pulsepress' );
		$title = apply_filters( 'widget_title', $title, $instance, $this->id_base );
		$number = ! empty( $instance['number'] ) ? absint( $instance['number'] ) : 5;
		$show_date = isset( $instance['show_date'] ) ? (bool) $instance['show_date'] : false;
		$show_thumbnail = isset( $instance['show_thumbnail'] ) ? (bool) $instance['show_thumbnail'] : true;

		$recent_posts = wp_get_recent_posts(
			array(
				'numberposts' => $number,
				'post_status' => 'publish',
			),
			OBJECT
		);

		echo $args['before_widget']; // phpcs:ignore WordPress.Security.EscapeOutput.OutputNotEscaped

		if ( $title ) {
			echo $args['before_title'] . $title . $args['after_title']; // phpcs:ignore WordPress.Security.EscapeOutput.OutputNotEscaped
		}

		if ( $recent_posts ) {
			echo '<ul class="recent-posts-widget">';
			foreach ( $recent_posts as $recent_post ) {
				echo '<li class="recent-post">';

				if ( $show_thumbnail && has_post_thumbnail( $recent_post->ID ) ) {
					echo '<div class="post-thumbnail">';
					echo '<a href="' . esc_url( get_permalink( $recent_post->ID ) ) . '">';
					echo get_the_post_thumbnail( $recent_post->ID, 'pulsepress-thumbnail' );
					echo '</a>';
					echo '</div>';
				}

				echo '<div class="post-content">';
				echo '<h4 class="post-title"><a href="' . esc_url( get_permalink( $recent_post->ID ) ) . '">' . esc_html( get_the_title( $recent_post->ID ) ) . '</a></h4>';

				if ( $show_date ) {
					echo '<span class="post-date">' . esc_html( get_the_date( '', $recent_post->ID ) ) . '</span>';
				}

				echo '</div>';
				echo '</li>';
			}
			echo '</ul>';
		}

		echo $args['after_widget']; // phpcs:ignore WordPress.Security.EscapeOutput.OutputNotEscaped
	}

	/**
	 * Back-end widget form.
	 *
	 * @param array $instance Previously saved values from database.
	 */
	public function form( $instance ) {
		$title = isset( $instance['title'] ) ? $instance['title'] : esc_html__( 'Recent Posts', 'pulsepress' );
		$number = isset( $instance['number'] ) ? absint( $instance['number'] ) : 5;
		$show_date = isset( $instance['show_date'] ) ? (bool) $instance['show_date'] : false;
		$show_thumbnail = isset( $instance['show_thumbnail'] ) ? (bool) $instance['show_thumbnail'] : true;
		?>
		<p>
			<label for="<?php echo esc_attr( $this->get_field_id( 'title' ) ); ?>"><?php esc_html_e( 'Title:', 'pulsepress' ); ?></label>
			<input class="widefat" id="<?php echo esc_attr( $this->get_field_id( 'title' ) ); ?>" name="<?php echo esc_attr( $this->get_field_name( 'title' ) ); ?>" type="text" value="<?php echo esc_attr( $title ); ?>">
		</p>
		<p>
			<label for="<?php echo esc_attr( $this->get_field_id( 'number' ) ); ?>"><?php esc_html_e( 'Number of posts to show:', 'pulsepress' ); ?></label>
			<input class="tiny-text" id="<?php echo esc_attr( $this->get_field_id( 'number' ) ); ?>" name="<?php echo esc_attr( $this->get_field_name( 'number' ) ); ?>" type="number" step="1" min="1" value="<?php echo esc_attr( $number ); ?>" size="3">
		</p>
		<p>
			<input class="checkbox" type="checkbox" <?php checked( $show_date ); ?> id="<?php echo esc_attr( $this->get_field_id( 'show_date' ) ); ?>" name="<?php echo esc_attr( $this->get_field_name( 'show_date' ) ); ?>">
			<label for="<?php echo esc_attr( $this->get_field_id( 'show_date' ) ); ?>"><?php esc_html_e( 'Display post date?', 'pulsepress' ); ?></label>
		</p>
		<p>
			<input class="checkbox" type="checkbox" <?php checked( $show_thumbnail ); ?> id="<?php echo esc_attr( $this->get_field_id( 'show_thumbnail' ) ); ?>" name="<?php echo esc_attr( $this->get_field_name( 'show_thumbnail' ) ); ?>">
			<label for="<?php echo esc_attr( $this->get_field_id( 'show_thumbnail' ) ); ?>"><?php esc_html_e( 'Display post thumbnail?', 'pulsepress' ); ?></label>
		</p>
		<?php
	}

	/**
	 * Sanitize widget form values as they are saved.
	 *
	 * @param array $new_instance Values just sent to be saved.
	 * @param array $old_instance Previously saved values from database.
	 *
	 * @return array Updated safe values to be saved.
	 */
	public function update( $new_instance, $old_instance ) {
		$instance = array();
		$instance['title'] = ( ! empty( $new_instance['title'] ) ) ? sanitize_text_field( $new_instance['title'] ) : '';
		$instance['number'] = ( ! empty( $new_instance['number'] ) ) ? absint( $new_instance['number'] ) : 5;
		$instance['show_date'] = isset( $new_instance['show_date'] ) ? (bool) $new_instance['show_date'] : false;
		$instance['show_thumbnail'] = isset( $new_instance['show_thumbnail'] ) ? (bool) $new_instance['show_thumbnail'] : true;

		return $instance;
	}
}

/**
 * Popular Posts Widget
 */
class PulsePress_Popular_Posts_Widget extends WP_Widget {

	/**
	 * Register widget with WordPress.
	 */
	public function __construct() {
		parent::__construct(
			'pulsepress_popular_posts',
			esc_html__( 'PulsePress: Popular Posts', 'pulsepress' ),
			array(
				'description' => esc_html__( 'Display popular posts by views or comments.', 'pulsepress' ),
			)
		);
	}

	/**
	 * Front-end display of widget.
	 *
	 * @param array $args     Widget arguments.
	 * @param array $instance Saved values from database.
	 */
	public function widget( $args, $instance ) {
		$title = ! empty( $instance['title'] ) ? $instance['title'] : esc_html__( 'Popular Posts', 'pulsepress' );
		$title = apply_filters( 'widget_title', $title, $instance, $this->id_base );
		$number = ! empty( $instance['number'] ) ? absint( $instance['number'] ) : 5;
		$orderby = ! empty( $instance['orderby'] ) ? $instance['orderby'] : 'comment_count';
		$show_thumbnail = isset( $instance['show_thumbnail'] ) ? (bool) $instance['show_thumbnail'] : true;

		$popular_args = array(
			'post_type'      => 'post',
			'posts_per_page' => $number,
			'post_status'    => 'publish',
		);

		if ( 'views' === $orderby ) {
			$popular_args['meta_key'] = 'post_views_count';
			$popular_args['orderby'] = 'meta_value_num';
		} else {
			$popular_args['orderby'] = 'comment_count';
		}

		$popular_args['order'] = 'DESC';

		$popular_query = new WP_Query( $popular_args );

		echo $args['before_widget']; // phpcs:ignore WordPress.Security.EscapeOutput.OutputNotEscaped

		if ( $title ) {
			echo $args['before_title'] . $title . $args['after_title']; // phpcs:ignore WordPress.Security.EscapeOutput.OutputNotEscaped
		}

		if ( $popular_query->have_posts() ) {
			echo '<ul class="popular-posts-widget">';
			while ( $popular_query->have_posts() ) {
				$popular_query->the_post();
				echo '<li class="popular-post">';

				if ( $show_thumbnail && has_post_thumbnail() ) {
					echo '<div class="post-thumbnail">';
					echo '<a href="' . esc_url( get_permalink() ) . '">';
					the_post_thumbnail( 'pulsepress-thumbnail' );
					echo '</a>';
					echo '</div>';
				}

				echo '<div class="post-content">';
				echo '<h4 class="post-title"><a href="' . esc_url( get_permalink() ) . '">' . esc_html( get_the_title() ) . '</a></h4>';

				if ( 'views' === $orderby ) {
					$views = get_post_meta( get_the_ID(), 'post_views_count', true );
					$views = $views ? $views : '0';
					echo '<span class="post-views"><i class="fas fa-eye"></i> ' . esc_html( $views ) . '</span>';
				} else {
					echo '<span class="post-comments"><i class="fas fa-comment"></i> ' . esc_html( get_comments_number() ) . '</span>';
				}

				echo '</div>';
				echo '</li>';
			}
			echo '</ul>';
			wp_reset_postdata();
		}

		echo $args['after_widget']; // phpcs:ignore WordPress.Security.EscapeOutput.OutputNotEscaped
	}

	/**
	 * Back-end widget form.
	 *
	 * @param array $instance Previously saved values from database.
	 */
	public function form( $instance ) {
		$title = isset( $instance['title'] ) ? $instance['title'] : esc_html__( 'Popular Posts', 'pulsepress' );
		$number = isset( $instance['number'] ) ? absint( $instance['number'] ) : 5;
		$orderby = isset( $instance['orderby'] ) ? $instance['orderby'] : 'comment_count';
		$show_thumbnail = isset( $instance['show_thumbnail'] ) ? (bool) $instance['show_thumbnail'] : true;
		?>
		<p>
			<label for="<?php echo esc_attr( $this->get_field_id( 'title' ) ); ?>"><?php esc_html_e( 'Title:', 'pulsepress' ); ?></label>
			<input class="widefat" id="<?php echo esc_attr( $this->get_field_id( 'title' ) ); ?>" name="<?php echo esc_attr( $this->get_field_name( 'title' ) ); ?>" type="text" value="<?php echo esc_attr( $title ); ?>">
		</p>
		<p>
			<label for="<?php echo esc_attr( $this->get_field_id( 'number' ) ); ?>"><?php esc_html_e( 'Number of posts to show:', 'pulsepress' ); ?></label>
			<input class="tiny-text" id="<?php echo esc_attr( $this->get_field_id( 'number' ) ); ?>" name="<?php echo esc_attr( $this->get_field_name( 'number' ) ); ?>" type="number" step="1" min="1" value="<?php echo esc_attr( $number ); ?>" size="3">
		</p>
		<p>
			<label for="<?php echo esc_attr( $this->get_field_id( 'orderby' ) ); ?>"><?php esc_html_e( 'Order by:', 'pulsepress' ); ?></label>
			<select id="<?php echo esc_attr( $this->get_field_id( 'orderby' ) ); ?>" name="<?php echo esc_attr( $this->get_field_name( 'orderby' ) ); ?>">
				<option value="comment_count" <?php selected( $orderby, 'comment_count' ); ?>><?php esc_html_e( 'Comment Count', 'pulsepress' ); ?></option>
				<option value="views" <?php selected( $orderby, 'views' ); ?>><?php esc_html_e( 'View Count', 'pulsepress' ); ?></option>
			</select>
		</p>
		<p>
			<input class="checkbox" type="checkbox" <?php checked( $show_thumbnail ); ?> id="<?php echo esc_attr( $this->get_field_id( 'show_thumbnail' ) ); ?>" name="<?php echo esc_attr( $this->get_field_name( 'show_thumbnail' ) ); ?>">
			<label for="<?php echo esc_attr( $this->get_field_id( 'show_thumbnail' ) ); ?>"><?php esc_html_e( 'Display post thumbnail?', 'pulsepress' ); ?></label>
		</p>
		<?php
	}

	/**
	 * Sanitize widget form values as they are saved.
	 *
	 * @param array $new_instance Values just sent to be saved.
	 * @param array $old_instance Previously saved values from database.
	 *
	 * @return array Updated safe values to be saved.
	 */
	public function update( $new_instance, $old_instance ) {
		$instance = array();
		$instance['title'] = ( ! empty( $new_instance['title'] ) ) ? sanitize_text_field( $new_instance['title'] ) : '';
		$instance['number'] = ( ! empty( $new_instance['number'] ) ) ? absint( $new_instance['number'] ) : 5;
		$instance['orderby'] = ( ! empty( $new_instance['orderby'] ) ) ? sanitize_text_field( $new_instance['orderby'] ) : 'comment_count';
		$instance['show_thumbnail'] = isset( $new_instance['show_thumbnail'] ) ? (bool) $new_instance['show_thumbnail'] : true;

		return $instance;
	}
}

/**
 * Social Media Widget
 */
class PulsePress_Social_Widget extends WP_Widget {

	/**
	 * Register widget with WordPress.
	 */
	public function __construct() {
		parent::__construct(
			'pulsepress_social',
			esc_html__( 'PulsePress: Social Media', 'pulsepress' ),
			array(
				'description' => esc_html__( 'Display social media links.', 'pulsepress' ),
			)
		);
	}

	/**
	 * Front-end display of widget.
	 *
	 * @param array $args     Widget arguments.
	 * @param array $instance Saved values from database.
	 */
	public function widget( $args, $instance ) {
		$title = ! empty( $instance['title'] ) ? $instance['title'] : esc_html__( 'Follow Us', 'pulsepress' );
		$title = apply_filters( 'widget_title', $title, $instance, $this->id_base );
		$facebook = ! empty( $instance['facebook'] ) ? $instance['facebook'] : '';
		$twitter = ! empty( $instance['twitter'] ) ? $instance['twitter'] : '';
		$instagram = ! empty( $instance['instagram'] ) ? $instance['instagram'] : '';
		$pinterest = ! empty( $instance['pinterest'] ) ? $instance['pinterest'] : '';
		$youtube = ! empty( $instance['youtube'] ) ? $instance['youtube'] : '';
		$linkedin = ! empty( $instance['linkedin'] ) ? $instance['linkedin'] : '';

		echo $args['before_widget']; // phpcs:ignore WordPress.Security.EscapeOutput.OutputNotEscaped

		if ( $title ) {
			echo $args['before_title'] . $title . $args['after_title']; // phpcs:ignore WordPress.Security.EscapeOutput.OutputNotEscaped
		}

		echo '<div class="social-links">';

		if ( $facebook ) {
			echo '<a href="' . esc_url( $facebook ) . '" target="_blank" class="facebook"><i class="fab fa-facebook-f"></i></a>';
		}

		if ( $twitter ) {
			echo '<a href="' . esc_url( $twitter ) . '" target="_blank" class="twitter"><i class="fab fa-twitter"></i></a>';
		}

		if ( $instagram ) {
			echo '<a href="' . esc_url( $instagram ) . '" target="_blank" class="instagram"><i class="fab fa-instagram"></i></a>';
		}

		if ( $pinterest ) {
			echo '<a href="' . esc_url( $pinterest ) . '" target="_blank" class="pinterest"><i class="fab fa-pinterest-p"></i></a>';
		}

		if ( $youtube ) {
			echo '<a href="' . esc_url( $youtube ) . '" target="_blank" class="youtube"><i class="fab fa-youtube"></i></a>';
		}

		if ( $linkedin ) {
			echo '<a href="' . esc_url( $linkedin ) . '" target="_blank" class="linkedin"><i class="fab fa-linkedin-in"></i></a>';
		}

		echo '</div>';

		echo $args['after_widget']; // phpcs:ignore WordPress.Security.EscapeOutput.OutputNotEscaped
	}

	/**
	 * Back-end widget form.
	 *
	 * @param array $instance Previously saved values from database.
	 */
	public function form( $instance ) {
		$title = isset( $instance['title'] ) ? $instance['title'] : esc_html__( 'Follow Us', 'pulsepress' );
		$facebook = isset( $instance['facebook'] ) ? $instance['facebook'] : '';
		$twitter = isset( $instance['twitter'] ) ? $instance['twitter'] : '';
		$instagram = isset( $instance['instagram'] ) ? $instance['instagram'] : '';
		$pinterest = isset( $instance['pinterest'] ) ? $instance['pinterest'] : '';
		$youtube = isset( $instance['youtube'] ) ? $instance['youtube'] : '';
		$linkedin = isset( $instance['linkedin'] ) ? $instance['linkedin'] : '';
		?>
		<p>
			<label for="<?php echo esc_attr( $this->get_field_id( 'title' ) ); ?>"><?php esc_html_e( 'Title:', 'pulsepress' ); ?></label>
			<input class="widefat" id="<?php echo esc_attr( $this->get_field_id( 'title' ) ); ?>" name="<?php echo esc_attr( $this->get_field_name( 'title' ) ); ?>" type="text" value="<?php echo esc_attr( $title ); ?>">
		</p>
		<p>
			<label for="<?php echo esc_attr( $this->get_field_id( 'facebook' ) ); ?>"><?php esc_html_e( 'Facebook URL:', 'pulsepress' ); ?></label>
			<input class="widefat" id="<?php echo esc_attr( $this->get_field_id( 'facebook' ) ); ?>" name="<?php echo esc_attr( $this->get_field_name( 'facebook' ) ); ?>" type="url" value="<?php echo esc_attr( $facebook ); ?>">
		</p>
		<p>
			<label for="<?php echo esc_attr( $this->get_field_id( 'twitter' ) ); ?>"><?php esc_html_e( 'Twitter URL:', 'pulsepress' ); ?></label>
			<input class="widefat" id="<?php echo esc_attr( $this->get_field_id( 'twitter' ) ); ?>" name="<?php echo esc_attr( $this->get_field_name( 'twitter' ) ); ?>" type="url" value="<?php echo esc_attr( $twitter ); ?>">
		</p>
		<p>
			<label for="<?php echo esc_attr( $this->get_field_id( 'instagram' ) ); ?>"><?php esc_html_e( 'Instagram URL:', 'pulsepress' ); ?></label>
			<input class="widefat" id="<?php echo esc_attr( $this->get_field_id( 'instagram' ) ); ?>" name="<?php echo esc_attr( $this->get_field_name( 'instagram' ) ); ?>" type="url" value="<?php echo esc_attr( $instagram ); ?>">
		</p>
		<p>
			<label for="<?php echo esc_attr( $this->get_field_id( 'pinterest' ) ); ?>"><?php esc_html_e( 'Pinterest URL:', 'pulsepress' ); ?></label>
			<input class="widefat" id="<?php echo esc_attr( $this->get_field_id( 'pinterest' ) ); ?>" name="<?php echo esc_attr( $this->get_field_name( 'pinterest' ) ); ?>" type="url" value="<?php echo esc_attr( $pinterest ); ?>">
		</p>
		<p>
			<label for="<?php echo esc_attr( $this->get_field_id( 'youtube' ) ); ?>"><?php esc_html_e( 'YouTube URL:', 'pulsepress' ); ?></label>
			<input class="widefat" id="<?php echo esc_attr( $this->get_field_id( 'youtube' ) ); ?>" name="<?php echo esc_attr( $this->get_field_name( 'youtube' ) ); ?>" type="url" value="<?php echo esc_attr( $youtube ); ?>">
		</p>
		<p>
			<label for="<?php echo esc_attr( $this->get_field_id( 'linkedin' ) ); ?>"><?php esc_html_e( 'LinkedIn URL:', 'pulsepress' ); ?></label>
			<input class="widefat" id="<?php echo esc_attr( $this->get_field_id( 'linkedin' ) ); ?>" name="<?php echo esc_attr( $this->get_field_name( 'linkedin' ) ); ?>" type="url" value="<?php echo esc_attr( $linkedin ); ?>">
		</p>
		<?php
	}

	/**
	 * Sanitize widget form values as they are saved.
	 *
	 * @param array $new_instance Values just sent to be saved.
	 * @param array $old_instance Previously saved values from database.
	 *
	 * @return array Updated safe values to be saved.
	 */
	public function update( $new_instance, $old_instance ) {
		$instance = array();
		$instance['title'] = ( ! empty( $new_instance['title'] ) ) ? sanitize_text_field( $new_instance['title'] ) : '';
		$instance['facebook'] = ( ! empty( $new_instance['facebook'] ) ) ? esc_url_raw( $new_instance['facebook'] ) : '';
		$instance['twitter'] = ( ! empty( $new_instance['twitter'] ) ) ? esc_url_raw( $new_instance['twitter'] ) : '';
		$instance['instagram'] = ( ! empty( $new_instance['instagram'] ) ) ? esc_url_raw( $new_instance['instagram'] ) : '';
		$instance['pinterest'] = ( ! empty( $new_instance['pinterest'] ) ) ? esc_url_raw( $new_instance['pinterest'] ) : '';
		$instance['youtube'] = ( ! empty( $new_instance['youtube'] ) ) ? esc_url_raw( $new_instance['youtube'] ) : '';
		$instance['linkedin'] = ( ! empty( $new_instance['linkedin'] ) ) ? esc_url_raw( $new_instance['linkedin'] ) : '';

		return $instance;
	}
}

/**
 * Newsletter Widget
 */
class PulsePress_Newsletter_Widget extends WP_Widget {

	/**
	 * Register widget with WordPress.
	 */
	public function __construct() {
		parent::__construct(
			'pulsepress_newsletter',
			esc_html__( 'PulsePress: Newsletter', 'pulsepress' ),
			array(
				'description' => esc_html__( 'Display a newsletter signup form.', 'pulsepress' ),
			)
		);
	}

	/**
	 * Front-end display of widget.
	 *
	 * @param array $args     Widget arguments.
	 * @param array $instance Saved values from database.
	 */
	public function widget( $args, $instance ) {
		$title = ! empty( $instance['title'] ) ? $instance['title'] : esc_html__( 'Newsletter', 'pulsepress' );
		$title = apply_filters( 'widget_title', $title, $instance, $this->id_base );
		$description = ! empty( $instance['description'] ) ? $instance['description'] : esc_html__( 'Subscribe to our newsletter to receive the latest news and updates.', 'pulsepress' );
		$mailchimp_url = ! empty( $instance['mailchimp_url'] ) ? $instance['mailchimp_url'] : '';
		$button_text = ! empty( $instance['button_text'] ) ? $instance['button_text'] : esc_html__( 'Subscribe', 'pulsepress' );

		echo $args['before_widget']; // phpcs:ignore WordPress.Security.EscapeOutput.OutputNotEscaped

		if ( $title ) {
			echo $args['before_title'] . $title . $args['after_title']; // phpcs:ignore WordPress.Security.EscapeOutput.OutputNotEscaped
		}

		echo '<div class="newsletter-widget">';

		if ( $description ) {
			echo '<p>' . esc_html( $description ) . '</p>';
		}

		if ( $mailchimp_url ) {
			// Mailchimp form
			echo '<form action="' . esc_url( $mailchimp_url ) . '" method="post" class="newsletter-form" target="_blank">';
			echo '<input type="email" name="EMAIL" placeholder="' . esc_attr__( 'Your Email', 'pulsepress' ) . '" required>';
			echo '<button type="submit">' . esc_html( $button_text ) . '</button>';
			echo '</form>';
		} else {
			// Default form (no action, for demonstration)
			echo '<form class="newsletter-form">';
			echo '<input type="email" placeholder="' . esc_attr__( 'Your Email', 'pulsepress' ) . '" required>';
			echo '<button type="submit">' . esc_html( $button_text ) . '</button>';
			echo '</form>';
			echo '<p class="form-note"><small>' . esc_html__( 'Note: This is a demo form. Set up Mailchimp URL in widget settings.', 'pulsepress' ) . '</small></p>';
		}

		echo '</div>';

		echo $args['after_widget']; // phpcs:ignore WordPress.Security.EscapeOutput.OutputNotEscaped
	}

	/**
	 * Back-end widget form.
	 *
	 * @param array $instance Previously saved values from database.
	 */
	public function form( $instance ) {
		$title = isset( $instance['title'] ) ? $instance['title'] : esc_html__( 'Newsletter', 'pulsepress' );
		$description = isset( $instance['description'] ) ? $instance['description'] : esc_html__( 'Subscribe to our newsletter to receive the latest news and updates.', 'pulsepress' );
		$mailchimp_url = isset( $instance['mailchimp_url'] ) ? $instance['mailchimp_url'] : '';
		$button_text = isset( $instance['button_text'] ) ? $instance['button_text'] : esc_html__( 'Subscribe', 'pulsepress' );
		?>
		<p>
			<label for="<?php echo esc_attr( $this->get_field_id( 'title' ) ); ?>"><?php esc_html_e( 'Title:', 'pulsepress' ); ?></label>
			<input class="widefat" id="<?php echo esc_attr( $this->get_field_id( 'title' ) ); ?>" name="<?php echo esc_attr( $this->get_field_name( 'title' ) ); ?>" type="text" value="<?php echo esc_attr( $title ); ?>">
		</p>
		<p>
			<label for="<?php echo esc_attr( $this->get_field_id( 'description' ) ); ?>"><?php esc_html_e( 'Description:', 'pulsepress' ); ?></label>
			<textarea class="widefat" id="<?php echo esc_attr( $this->get_field_id( 'description' ) ); ?>" name="<?php echo esc_attr( $this->get_field_name( 'description' ) ); ?>" rows="3"><?php echo esc_textarea( $description ); ?></textarea>
		</p>
		<p>
			<label for="<?php echo esc_attr( $this->get_field_id( 'mailchimp_url' ) ); ?>"><?php esc_html_e( 'Mailchimp Form Action URL:', 'pulsepress' ); ?></label>
			<input class="widefat" id="<?php echo esc_attr( $this->get_field_id( 'mailchimp_url' ) ); ?>" name="<?php echo esc_attr( $this->get_field_name( 'mailchimp_url' ) ); ?>" type="url" value="<?php echo esc_attr( $mailchimp_url ); ?>">
			<small><?php esc_html_e( 'The form action URL from your Mailchimp embedded form code.', 'pulsepress' ); ?></small>
		</p>
		<p>
			<label for="<?php echo esc_attr( $this->get_field_id( 'button_text' ) ); ?>"><?php esc_html_e( 'Button Text:', 'pulsepress' ); ?></label>
			<input class="widefat" id="<?php echo esc_attr( $this->get_field_id( 'button_text' ) ); ?>" name="<?php echo esc_attr( $this->get_field_name( 'button_text' ) ); ?>" type="text" value="<?php echo esc_attr( $button_text ); ?>">
		</p>
		<?php
	}

	/**
	 * Sanitize widget form values as they are saved.
	 *
	 * @param array $new_instance Values just sent to be saved.
	 * @param array $old_instance Previously saved values from database.
	 *
	 * @return array Updated safe values to be saved.
	 */
	public function update( $new_instance, $old_instance ) {
		$instance = array();
		$instance['title'] = ( ! empty( $new_instance['title'] ) ) ? sanitize_text_field( $new_instance['title'] ) : '';
		$instance['description'] = ( ! empty( $new_instance['description'] ) ) ? sanitize_textarea_field( $new_instance['description'] ) : '';
		$instance['mailchimp_url'] = ( ! empty( $new_instance['mailchimp_url'] ) ) ? esc_url_raw( $new_instance['mailchimp_url'] ) : '';
		$instance['button_text'] = ( ! empty( $new_instance['button_text'] ) ) ? sanitize_text_field( $new_instance['button_text'] ) : '';

		return $instance;
	}
}