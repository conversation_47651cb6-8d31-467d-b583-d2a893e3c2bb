<?php
/**
 * Accessibility Improvements
 *
 * @package PulsePress
 */

// Prevent direct access
if ( ! defined( 'ABSPATH' ) ) {
    exit;
}

/**
 * Initialize accessibility features
 */
function pulsepress_init_accessibility() {
    add_action( 'wp_enqueue_scripts', 'pulsepress_enqueue_accessibility_scripts' );
    add_action( 'wp_head', 'pulsepress_add_accessibility_styles' );
    add_filter( 'nav_menu_link_attributes', 'pulsepress_add_menu_accessibility_attributes', 10, 4 );
    add_filter( 'wp_nav_menu_items', 'pulsepress_add_menu_accessibility_markup', 10, 2 );
    add_filter( 'the_content', 'pulsepress_improve_content_accessibility' );
    add_action( 'wp_footer', 'pulsepress_add_skip_links' );
    add_action( 'wp_footer', 'pulsepress_add_accessibility_toolbar' );
}
add_action( 'init', 'pulsepress_init_accessibility' );

/**
 * Enqueue accessibility scripts
 */
function pulsepress_enqueue_accessibility_scripts() {
    wp_enqueue_script(
        'pulsepress-accessibility',
        get_template_directory_uri() . '/assets/js/accessibility.js',
        array( 'jquery' ),
        PULSEPRESS_VERSION,
        true
    );
    
    wp_localize_script(
        'pulsepress-accessibility',
        'pulsepressA11y',
        array(
            'strings' => array(
                'skip_to_content'    => esc_html__( 'Skip to main content', 'pulsepress' ),
                'skip_to_navigation' => esc_html__( 'Skip to navigation', 'pulsepress' ),
                'menu_expanded'      => esc_html__( 'Menu expanded', 'pulsepress' ),
                'menu_collapsed'     => esc_html__( 'Menu collapsed', 'pulsepress' ),
                'submenu_expanded'   => esc_html__( 'Submenu expanded', 'pulsepress' ),
                'submenu_collapsed'  => esc_html__( 'Submenu collapsed', 'pulsepress' ),
            ),
        )
    );
}

/**
 * Add accessibility styles
 */
function pulsepress_add_accessibility_styles() {
    ?>
    <style id="pulsepress-accessibility-styles">
    /* Skip links */
    .skip-links {
        position: absolute;
        top: -40px;
        left: 6px;
        z-index: 999999;
    }
    
    .skip-links a {
        position: absolute;
        left: -10000px;
        top: auto;
        width: 1px;
        height: 1px;
        overflow: hidden;
        background: var(--primary-color);
        color: white;
        padding: 8px 16px;
        text-decoration: none;
        border-radius: 0 0 4px 4px;
        font-weight: 600;
    }
    
    .skip-links a:focus {
        position: static;
        width: auto;
        height: auto;
        left: auto;
        top: auto;
        overflow: visible;
    }
    
    /* Focus styles */
    *:focus {
        outline: 2px solid var(--primary-color);
        outline-offset: 2px;
    }
    
    /* High contrast mode support */
    @media (prefers-contrast: high) {
        :root {
            --primary-color: #000000;
            --secondary-color: #000000;
            --text-color: #000000;
            --bg-color: #ffffff;
        }
        
        [data-theme="dark"] {
            --primary-color: #ffffff;
            --secondary-color: #ffffff;
            --text-color: #ffffff;
            --bg-color: #000000;
        }
        
        .post-card,
        .widget,
        .comment {
            border: 2px solid currentColor;
        }
    }
    
    /* Reduced motion support */
    @media (prefers-reduced-motion: reduce) {
        *,
        *::before,
        *::after {
            animation-duration: 0.01ms !important;
            animation-iteration-count: 1 !important;
            transition-duration: 0.01ms !important;
            scroll-behavior: auto !important;
        }
    }
    
    /* Screen reader only text */
    .screen-reader-text {
        clip: rect(1px, 1px, 1px, 1px);
        position: absolute !important;
        height: 1px;
        width: 1px;
        overflow: hidden;
        word-wrap: normal !important;
    }
    
    .screen-reader-text:focus {
        background-color: #f1f1f1;
        border-radius: 3px;
        box-shadow: 0 0 2px 2px rgba(0, 0, 0, 0.6);
        clip: auto !important;
        color: #21759b;
        display: block;
        font-size: 14px;
        font-weight: bold;
        height: auto;
        left: 5px;
        line-height: normal;
        padding: 15px 23px 14px;
        text-decoration: none;
        top: 5px;
        width: auto;
        z-index: 100000;
    }
    
    /* Accessibility toolbar */
    .accessibility-toolbar {
        position: fixed;
        top: 50%;
        right: 0;
        transform: translateY(-50%);
        background: var(--primary-color);
        color: white;
        padding: 10px;
        border-radius: 8px 0 0 8px;
        z-index: 9999;
        box-shadow: -2px 0 10px rgba(0, 0, 0, 0.2);
    }
    
    .accessibility-toolbar button {
        display: block;
        width: 40px;
        height: 40px;
        margin: 5px 0;
        background: transparent;
        border: 2px solid white;
        color: white;
        border-radius: 4px;
        cursor: pointer;
        font-size: 16px;
        transition: all 0.3s ease;
    }
    
    .accessibility-toolbar button:hover,
    .accessibility-toolbar button:focus {
        background: white;
        color: var(--primary-color);
    }
    
    .accessibility-toolbar button.active {
        background: var(--secondary-color);
        border-color: var(--secondary-color);
    }
    
    /* Font size adjustments */
    body.font-size-small {
        font-size: 14px;
    }
    
    body.font-size-large {
        font-size: 18px;
    }
    
    body.font-size-xl {
        font-size: 20px;
    }
    
    /* High contrast mode */
    body.high-contrast {
        filter: contrast(150%);
    }
    
    body.high-contrast img {
        filter: contrast(120%);
    }
    
    /* Dyslexia-friendly font */
    body.dyslexia-font,
    body.dyslexia-font * {
        font-family: 'OpenDyslexic', Arial, sans-serif !important;
    }
    </style>
    <?php
}

/**
 * Add accessibility attributes to menu items
 */
function pulsepress_add_menu_accessibility_attributes( $atts, $item, $args, $depth ) {
    // Add ARIA attributes for dropdown menus
    if ( in_array( 'menu-item-has-children', $item->classes ) ) {
        $atts['aria-haspopup'] = 'true';
        $atts['aria-expanded'] = 'false';
        $atts['role'] = 'button';
    }
    
    // Add current page indicator
    if ( in_array( 'current-menu-item', $item->classes ) ) {
        $atts['aria-current'] = 'page';
    }
    
    return $atts;
}

/**
 * Add accessibility markup to menus
 */
function pulsepress_add_menu_accessibility_markup( $items, $args ) {
    // Add screen reader text for external links
    $items = preg_replace_callback(
        '/<a([^>]*)href="([^"]*)"([^>]*)>([^<]*)<\/a>/',
        function( $matches ) {
            $url = $matches[2];
            $text = $matches[4];
            
            // Check if it's an external link
            if ( strpos( $url, home_url() ) === false && strpos( $url, 'http' ) === 0 ) {
                $text .= '<span class="screen-reader-text"> ' . esc_html__( '(opens in a new tab)', 'pulsepress' ) . '</span>';
            }
            
            return '<a' . $matches[1] . 'href="' . $matches[2] . '"' . $matches[3] . '>' . $text . '</a>';
        },
        $items
    );
    
    return $items;
}

/**
 * Improve content accessibility
 */
function pulsepress_improve_content_accessibility( $content ) {
    // Add alt text to images without it
    $content = preg_replace_callback(
        '/<img([^>]*?)src="([^"]*)"([^>]*?)>/',
        function( $matches ) {
            $before = $matches[1];
            $src = $matches[2];
            $after = $matches[3];
            
            // Check if alt attribute exists
            if ( strpos( $before . $after, 'alt=' ) === false ) {
                $filename = basename( $src );
                $alt_text = pathinfo( $filename, PATHINFO_FILENAME );
                $alt_text = str_replace( array( '-', '_' ), ' ', $alt_text );
                $alt_text = ucwords( $alt_text );
                
                $after .= ' alt="' . esc_attr( $alt_text ) . '"';
            }
            
            return '<img' . $before . 'src="' . $src . '"' . $after . '>';
        },
        $content
    );
    
    // Add title attributes to links without them
    $content = preg_replace_callback(
        '/<a([^>]*?)href="([^"]*)"([^>]*?)>([^<]*)<\/a>/',
        function( $matches ) {
            $before = $matches[1];
            $href = $matches[2];
            $after = $matches[3];
            $text = $matches[4];
            
            // Check if title attribute exists
            if ( strpos( $before . $after, 'title=' ) === false ) {
                $title = strip_tags( $text );
                if ( strlen( $title ) > 50 ) {
                    $title = substr( $title, 0, 47 ) . '...';
                }
                $after .= ' title="' . esc_attr( $title ) . '"';
            }
            
            return '<a' . $before . 'href="' . $href . '"' . $after . '>' . $text . '</a>';
        },
        $content
    );
    
    return $content;
}

/**
 * Add skip links
 */
function pulsepress_add_skip_links() {
    ?>
    <div class="skip-links">
        <a href="#main" class="skip-link"><?php esc_html_e( 'Skip to main content', 'pulsepress' ); ?></a>
        <a href="#primary-menu" class="skip-link"><?php esc_html_e( 'Skip to navigation', 'pulsepress' ); ?></a>
        <a href="#sidebar" class="skip-link"><?php esc_html_e( 'Skip to sidebar', 'pulsepress' ); ?></a>
        <a href="#footer" class="skip-link"><?php esc_html_e( 'Skip to footer', 'pulsepress' ); ?></a>
    </div>
    <?php
}

/**
 * Add accessibility toolbar
 */
function pulsepress_add_accessibility_toolbar() {
    if ( ! get_theme_mod( 'pulsepress_accessibility_toolbar', true ) ) {
        return;
    }
    ?>
    <div class="accessibility-toolbar" role="toolbar" aria-label="<?php esc_attr_e( 'Accessibility Options', 'pulsepress' ); ?>">
        <button type="button" id="font-size-decrease" aria-label="<?php esc_attr_e( 'Decrease font size', 'pulsepress' ); ?>" title="<?php esc_attr_e( 'Decrease font size', 'pulsepress' ); ?>">
            A-
        </button>
        <button type="button" id="font-size-increase" aria-label="<?php esc_attr_e( 'Increase font size', 'pulsepress' ); ?>" title="<?php esc_attr_e( 'Increase font size', 'pulsepress' ); ?>">
            A+
        </button>
        <button type="button" id="high-contrast-toggle" aria-label="<?php esc_attr_e( 'Toggle high contrast', 'pulsepress' ); ?>" title="<?php esc_attr_e( 'Toggle high contrast', 'pulsepress' ); ?>">
            ◐
        </button>
        <button type="button" id="dyslexia-font-toggle" aria-label="<?php esc_attr_e( 'Toggle dyslexia-friendly font', 'pulsepress' ); ?>" title="<?php esc_attr_e( 'Toggle dyslexia-friendly font', 'pulsepress' ); ?>">
            Aa
        </button>
        <button type="button" id="focus-mode-toggle" aria-label="<?php esc_attr_e( 'Toggle focus mode', 'pulsepress' ); ?>" title="<?php esc_attr_e( 'Toggle focus mode', 'pulsepress' ); ?>">
            ●
        </button>
    </div>
    <?php
}

/**
 * Add accessibility settings to customizer
 */
function pulsepress_accessibility_customizer( $wp_customize ) {
    // Accessibility Section
    $wp_customize->add_section(
        'pulsepress_accessibility',
        array(
            'title'    => esc_html__( 'Accessibility', 'pulsepress' ),
            'priority' => 210,
        )
    );
    
    // Accessibility Toolbar
    $wp_customize->add_setting(
        'pulsepress_accessibility_toolbar',
        array(
            'default'           => true,
            'sanitize_callback' => 'pulsepress_sanitize_checkbox',
        )
    );
    
    $wp_customize->add_control(
        'pulsepress_accessibility_toolbar',
        array(
            'label'       => esc_html__( 'Show Accessibility Toolbar', 'pulsepress' ),
            'description' => esc_html__( 'Display accessibility options toolbar on the side', 'pulsepress' ),
            'section'     => 'pulsepress_accessibility',
            'type'        => 'checkbox',
        )
    );
    
    // Skip Links
    $wp_customize->add_setting(
        'pulsepress_skip_links',
        array(
            'default'           => true,
            'sanitize_callback' => 'pulsepress_sanitize_checkbox',
        )
    );
    
    $wp_customize->add_control(
        'pulsepress_skip_links',
        array(
            'label'       => esc_html__( 'Enable Skip Links', 'pulsepress' ),
            'description' => esc_html__( 'Add skip navigation links for keyboard users', 'pulsepress' ),
            'section'     => 'pulsepress_accessibility',
            'type'        => 'checkbox',
        )
    );
    
    // Focus Indicators
    $wp_customize->add_setting(
        'pulsepress_focus_indicators',
        array(
            'default'           => true,
            'sanitize_callback' => 'pulsepress_sanitize_checkbox',
        )
    );
    
    $wp_customize->add_control(
        'pulsepress_focus_indicators',
        array(
            'label'       => esc_html__( 'Enhanced Focus Indicators', 'pulsepress' ),
            'description' => esc_html__( 'Show clear focus indicators for keyboard navigation', 'pulsepress' ),
            'section'     => 'pulsepress_accessibility',
            'type'        => 'checkbox',
        )
    );
}
add_action( 'customize_register', 'pulsepress_accessibility_customizer' );

/**
 * Add ARIA landmarks to theme
 */
function pulsepress_add_aria_landmarks() {
    // This function can be called in template files to add proper ARIA landmarks
    // Implementation depends on theme structure
}

/**
 * Validate color contrast
 */
function pulsepress_validate_color_contrast( $foreground, $background ) {
    // Convert hex to RGB
    $fg_rgb = pulsepress_hex_to_rgb( $foreground );
    $bg_rgb = pulsepress_hex_to_rgb( $background );
    
    // Calculate relative luminance
    $fg_luminance = pulsepress_get_relative_luminance( $fg_rgb );
    $bg_luminance = pulsepress_get_relative_luminance( $bg_rgb );
    
    // Calculate contrast ratio
    $contrast_ratio = ( max( $fg_luminance, $bg_luminance ) + 0.05 ) / ( min( $fg_luminance, $bg_luminance ) + 0.05 );
    
    return $contrast_ratio;
}

/**
 * Convert hex color to RGB
 */
function pulsepress_hex_to_rgb( $hex ) {
    $hex = ltrim( $hex, '#' );
    
    if ( strlen( $hex ) === 3 ) {
        $hex = $hex[0] . $hex[0] . $hex[1] . $hex[1] . $hex[2] . $hex[2];
    }
    
    return array(
        'r' => hexdec( substr( $hex, 0, 2 ) ),
        'g' => hexdec( substr( $hex, 2, 2 ) ),
        'b' => hexdec( substr( $hex, 4, 2 ) ),
    );
}

/**
 * Get relative luminance
 */
function pulsepress_get_relative_luminance( $rgb ) {
    $r = $rgb['r'] / 255;
    $g = $rgb['g'] / 255;
    $b = $rgb['b'] / 255;
    
    $r = ( $r <= 0.03928 ) ? $r / 12.92 : pow( ( $r + 0.055 ) / 1.055, 2.4 );
    $g = ( $g <= 0.03928 ) ? $g / 12.92 : pow( ( $g + 0.055 ) / 1.055, 2.4 );
    $b = ( $b <= 0.03928 ) ? $b / 12.92 : pow( ( $b + 0.055 ) / 1.055, 2.4 );
    
    return 0.2126 * $r + 0.7152 * $g + 0.0722 * $b;
}
