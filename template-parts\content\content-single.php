<?php
/**
 * Template part for displaying single posts
 *
 * @link https://developer.wordpress.org/themes/basics/template-hierarchy/
 *
 * @package PulsePress
 */

?>

<article id="post-<?php the_ID(); ?>" <?php post_class('single-post'); ?>>
	<header class="entry-header">
		<div class="post-category">
			<?php
			$categories = get_the_category();
			if ( ! empty( $categories ) ) {
				foreach ( $categories as $category ) {
					echo '<a href="' . esc_url( get_category_link( $category->term_id ) ) . '">' . esc_html( $category->name ) . '</a>';
				}
			}
			?>
		</div>
		
		<?php the_title( '<h1 class="entry-title">', '</h1>' ); ?>
		
		<div class="entry-meta">
			<?php
			pulsepress_posted_by();
			pulsepress_posted_on();
			
			// Estimated reading time
			$content = get_post_field( 'post_content', get_the_ID() );
			$word_count = str_word_count( strip_tags( $content ) );
			$reading_time = ceil( $word_count / 200 ); // Assuming 200 words per minute reading speed
			?>
			<span class="reading-time">
				<i class="far fa-clock"></i>
				<?php 
				/* translators: %d: Reading time in minutes */
				printf( esc_html( _n( '%d min read', '%d min read', $reading_time, 'pulsepress' ) ), $reading_time );
				?>
			</span>
		</div><!-- .entry-meta -->
	</header><!-- .entry-header -->

	<?php if ( has_post_thumbnail() ) : ?>
		<div class="post-thumbnail">
			<?php the_post_thumbnail( 'pulsepress-featured' ); ?>
			<?php if ( get_the_post_thumbnail_caption() ) : ?>
				<div class="post-thumbnail-caption">
					<?php the_post_thumbnail_caption(); ?>
				</div>
			<?php endif; ?>
		</div>
	<?php endif; ?>

	<div class="entry-content">
		<?php
		the_content(
			sprintf(
				wp_kses(
					/* translators: %s: Name of current post. Only visible to screen readers */
					__( 'Continue reading<span class="screen-reader-text"> "%s"</span>', 'pulsepress' ),
					array(
						'span' => array(
							'class' => array(),
						),
					)
				),
				wp_kses_post( get_the_title() )
			)
		);

		wp_link_pages(
			array(
				'before' => '<div class="page-links">' . esc_html__( 'Pages:', 'pulsepress' ),
				'after'  => '</div>',
			)
		);
		?>
	</div><!-- .entry-content -->

	<footer class="entry-footer">
		<?php pulsepress_entry_footer(); ?>
		
		<!-- Social Share Buttons -->
		<div class="social-share">
			<h4><?php esc_html_e( 'Share This Post', 'pulsepress' ); ?></h4>
			<div class="social-share-buttons">
				<a href="#" class="social-share-button" data-platform="facebook" data-url="<?php the_permalink(); ?>" data-title="<?php the_title(); ?>">
					<i class="fab fa-facebook-f"></i>
					<span class="screen-reader-text"><?php esc_html_e( 'Share on Facebook', 'pulsepress' ); ?></span>
				</a>
				<a href="#" class="social-share-button" data-platform="twitter" data-url="<?php the_permalink(); ?>" data-title="<?php the_title(); ?>">
					<i class="fab fa-twitter"></i>
					<span class="screen-reader-text"><?php esc_html_e( 'Share on Twitter', 'pulsepress' ); ?></span>
				</a>
				<a href="#" class="social-share-button" data-platform="pinterest" data-url="<?php the_permalink(); ?>" data-title="<?php the_title(); ?>" data-image="<?php the_post_thumbnail_url( 'full' ); ?>">
					<i class="fab fa-pinterest-p"></i>
					<span class="screen-reader-text"><?php esc_html_e( 'Share on Pinterest', 'pulsepress' ); ?></span>
				</a>
				<a href="#" class="social-share-button" data-platform="linkedin" data-url="<?php the_permalink(); ?>" data-title="<?php the_title(); ?>">
					<i class="fab fa-linkedin-in"></i>
					<span class="screen-reader-text"><?php esc_html_e( 'Share on LinkedIn', 'pulsepress' ); ?></span>
				</a>
				<a href="#" class="social-share-button" data-platform="email" data-url="<?php the_permalink(); ?>" data-title="<?php the_title(); ?>">
					<i class="fas fa-envelope"></i>
					<span class="screen-reader-text"><?php esc_html_e( 'Share via Email', 'pulsepress' ); ?></span>
				</a>
			</div>
		</div>
	</footer><!-- .entry-footer -->
	
	<!-- Author Bio -->
	<?php $author_id = get_the_author_meta( 'ID' ); ?>
	<?php if ( $author_id ) : ?>
	<div class="author-bio">
		<div class="author-avatar">
			<?php echo get_avatar( $author_id, 100 ); ?>
		</div>
		<div class="author-info">
			<h3 class="author-name"><?php the_author(); ?></h3>
			<?php if ( get_the_author_meta( 'description' ) ) : ?>
				<p class="author-description"><?php the_author_meta( 'description' ); ?></p>
			<?php endif; ?>
			<div class="author-links">
				<?php if ( get_the_author_meta( 'user_url' ) ) : ?>
					<a href="<?php echo esc_url( get_the_author_meta( 'user_url' ) ); ?>" target="_blank"><i class="fas fa-globe"></i></a>
				<?php endif; ?>
				<?php if ( get_the_author_meta( 'twitter' ) ) : ?>
					<a href="<?php echo esc_url( 'https://twitter.com/' . get_the_author_meta( 'twitter' ) ); ?>" target="_blank"><i class="fab fa-twitter"></i></a>
				<?php endif; ?>
				<?php if ( get_the_author_meta( 'facebook' ) ) : ?>
					<a href="<?php echo esc_url( get_the_author_meta( 'facebook' ) ); ?>" target="_blank"><i class="fab fa-facebook-f"></i></a>
				<?php endif; ?>
				<?php if ( get_the_author_meta( 'instagram' ) ) : ?>
					<a href="<?php echo esc_url( get_the_author_meta( 'instagram' ) ); ?>" target="_blank"><i class="fab fa-instagram"></i></a>
				<?php endif; ?>
			</div>
			<a href="<?php echo esc_url( get_author_posts_url( $author_id ) ); ?>" class="author-link"><?php esc_html_e( 'View all posts', 'pulsepress' ); ?> <i class="fas fa-arrow-right"></i></a>
		</div>
	</div>
	<?php endif; ?>
</article><!-- #post-<?php the_ID(); ?> -->
