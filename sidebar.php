<?php
/**
 * The sidebar containing the main widget area
 *
 * @link https://developer.wordpress.org/themes/basics/template-files/#template-partials
 *
 * @package PulsePress
 */

if ( ! is_active_sidebar( 'sidebar-1' ) ) {
	return;
}
?>

<aside id="secondary" class="widget-area">
	<?php dynamic_sidebar( 'sidebar-1' ); ?>
	
	<?php if ( ! is_active_sidebar( 'sidebar-1' ) ) : ?>
		<!-- Default sidebar content if no widgets are active -->
		<section class="widget widget_search">
			<h2 class="widget-title"><?php esc_html_e( 'Search', 'pulsepress' ); ?></h2>
			<?php get_search_form(); ?>
		</section>
		
		<section class="widget widget_recent_posts">
			<h2 class="widget-title"><?php esc_html_e( 'Recent Posts', 'pulsepress' ); ?></h2>
			<ul>
				<?php
				$recent_posts = wp_get_recent_posts(
					array(
						'numberposts' => 5,
						'post_status' => 'publish',
					)
				);
				
				foreach ( $recent_posts as $post ) {
					?>
					<li class="recent-post">
						<?php if ( has_post_thumbnail( $post['ID'] ) ) : ?>
							<div class="post-thumbnail">
								<a href="<?php echo esc_url( get_permalink( $post['ID'] ) ); ?>">
									<?php echo get_the_post_thumbnail( $post['ID'], 'pulsepress-thumbnail' ); ?>
								</a>
							</div>
						<?php endif; ?>
						<div class="post-content">
							<h3 class="post-title"><a href="<?php echo esc_url( get_permalink( $post['ID'] ) ); ?>"><?php echo esc_html( $post['post_title'] ); ?></a></h3>
							<span class="post-date"><?php echo esc_html( get_the_date( '', $post['ID'] ) ); ?></span>
						</div>
					</li>
					<?php
				}
				wp_reset_postdata();
				?>
			</ul>
		</section>
		
		<section class="widget widget_popular_posts">
			<h2 class="widget-title"><?php esc_html_e( 'Popular Posts', 'pulsepress' ); ?></h2>
			<ul>
				<?php
				// Get popular posts (most commented)
				$popular_args = array(
					'post_type'      => 'post',
					'posts_per_page' => 5,
					'orderby'        => 'comment_count',
					'order'          => 'DESC',
				);
				
				$popular_query = new WP_Query( $popular_args );
				
				if ( $popular_query->have_posts() ) :
					while ( $popular_query->have_posts() ) :
						$popular_query->the_post();
						?>
						<li class="popular-post">
							<?php if ( has_post_thumbnail() ) : ?>
								<div class="post-thumbnail">
									<a href="<?php the_permalink(); ?>">
										<?php the_post_thumbnail( 'pulsepress-thumbnail' ); ?>
									</a>
								</div>
							<?php endif; ?>
							<div class="post-content">
								<h3 class="post-title"><a href="<?php the_permalink(); ?>"><?php the_title(); ?></a></h3>
								<span class="post-comments"><i class="fas fa-comment"></i> <?php comments_number( '0', '1', '%' ); ?></span>
							</div>
						</li>
						<?php
					endwhile;
					wp_reset_postdata();
				endif;
				?>
			</ul>
		</section>
		
		<section class="widget widget_categories">
			<h2 class="widget-title"><?php esc_html_e( 'Categories', 'pulsepress' ); ?></h2>
			<ul>
				<?php
				wp_list_categories(
					array(
						'title_li'    => '',
						'show_count'  => true,
					)
				);
				?>
			</ul>
		</section>
		
		<section class="widget widget_tags">
			<h2 class="widget-title"><?php esc_html_e( 'Tags', 'pulsepress' ); ?></h2>
			<div class="tagcloud">
				<?php
				$tags = get_tags(
					array(
						'number' => 20,
					)
				);
				
				if ( $tags ) {
					foreach ( $tags as $tag ) {
						echo '<a href="' . esc_url( get_tag_link( $tag->term_id ) ) . '" class="tag-cloud-link">' . esc_html( $tag->name ) . '</a>';
					}
				}
				?>
			</div>
		</section>
		
		<section class="widget widget_social">
			<h2 class="widget-title"><?php esc_html_e( 'Follow Us', 'pulsepress' ); ?></h2>
			<div class="social-links">
				<a href="#" target="_blank" class="facebook"><i class="fab fa-facebook-f"></i></a>
				<a href="#" target="_blank" class="twitter"><i class="fab fa-twitter"></i></a>
				<a href="#" target="_blank" class="instagram"><i class="fab fa-instagram"></i></a>
				<a href="#" target="_blank" class="pinterest"><i class="fab fa-pinterest-p"></i></a>
				<a href="#" target="_blank" class="youtube"><i class="fab fa-youtube"></i></a>
			</div>
		</section>
		
		<section class="widget widget_newsletter">
			<h2 class="widget-title"><?php esc_html_e( 'Newsletter', 'pulsepress' ); ?></h2>
			<div class="newsletter-widget">
				<p><?php esc_html_e( 'Subscribe to our newsletter to receive the latest news and updates.', 'pulsepress' ); ?></p>
				<form class="newsletter-form">
					<input type="email" placeholder="<?php esc_attr_e( 'Your Email', 'pulsepress' ); ?>" required>
					<button type="submit"><i class="fas fa-paper-plane"></i></button>
				</form>
			</div>
		</section>
	<?php endif; ?>
</aside><!-- #secondary -->
