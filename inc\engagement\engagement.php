<?php
/**
 * User Engagement Features
 *
 * @package PulsePress
 */

// Prevent direct access
if ( ! defined( 'ABSPATH' ) ) {
    exit;
}

/**
 * Initialize engagement features
 */
function pulsepress_init_engagement() {
    add_action( 'wp_enqueue_scripts', 'pulsepress_enqueue_engagement_scripts' );
    add_action( 'wp_ajax_load_more_posts', 'pulsepress_ajax_load_more_posts' );
    add_action( 'wp_ajax_nopriv_load_more_posts', 'pulsepress_ajax_load_more_posts' );
    add_action( 'wp_ajax_post_reaction', 'pulsepress_ajax_post_reaction' );
    add_action( 'wp_ajax_nopriv_post_reaction', 'pulsepress_ajax_post_reaction' );
    add_action( 'wp_footer', 'pulsepress_add_reading_progress_bar' );
    add_filter( 'the_content', 'pulsepress_add_reading_time_and_reactions' );
    add_shortcode( 'infinite_scroll', 'pulsepress_infinite_scroll_shortcode' );
}
add_action( 'init', 'pulsepress_init_engagement' );

/**
 * Enqueue engagement scripts and styles
 */
function pulsepress_enqueue_engagement_scripts() {
    wp_enqueue_script(
        'pulsepress-engagement',
        get_template_directory_uri() . '/assets/js/engagement.js',
        array( 'jquery' ),
        PULSEPRESS_VERSION,
        true
    );
    
    wp_localize_script(
        'pulsepress-engagement',
        'pulsepress_engagement',
        array(
            'ajax_url' => admin_url( 'admin-ajax.php' ),
            'nonce'    => wp_create_nonce( 'pulsepress_engagement_nonce' ),
            'strings'  => array(
                'loading'     => esc_html__( 'Loading...', 'pulsepress' ),
                'load_more'   => esc_html__( 'Load More', 'pulsepress' ),
                'no_more'     => esc_html__( 'No more posts', 'pulsepress' ),
                'error'       => esc_html__( 'Error loading posts', 'pulsepress' ),
            ),
        )
    );
    
    wp_enqueue_style(
        'pulsepress-engagement',
        get_template_directory_uri() . '/assets/css/engagement.css',
        array(),
        PULSEPRESS_VERSION
    );
}

/**
 * AJAX handler for loading more posts (infinite scroll)
 */
function pulsepress_ajax_load_more_posts() {
    check_ajax_referer( 'pulsepress_engagement_nonce', 'nonce' );
    
    $page = intval( $_POST['page'] );
    $posts_per_page = intval( $_POST['posts_per_page'] );
    $category = sanitize_text_field( $_POST['category'] );
    
    $args = array(
        'post_type'      => 'post',
        'post_status'    => 'publish',
        'posts_per_page' => $posts_per_page,
        'paged'          => $page,
    );
    
    if ( ! empty( $category ) && $category !== 'all' ) {
        $args['category_name'] = $category;
    }
    
    $query = new WP_Query( $args );
    
    if ( $query->have_posts() ) {
        ob_start();
        while ( $query->have_posts() ) {
            $query->the_post();
            get_template_part( 'template-parts/content/content', 'excerpt' );
        }
        $content = ob_get_clean();
        wp_reset_postdata();
        
        wp_send_json_success( array(
            'content'   => $content,
            'has_more'  => $page < $query->max_num_pages,
            'next_page' => $page + 1,
        ) );
    } else {
        wp_send_json_error( array( 'message' => 'No more posts found' ) );
    }
}

/**
 * AJAX handler for post reactions
 */
function pulsepress_ajax_post_reaction() {
    check_ajax_referer( 'pulsepress_engagement_nonce', 'nonce' );
    
    $post_id = intval( $_POST['post_id'] );
    $reaction = sanitize_text_field( $_POST['reaction'] );
    
    if ( ! $post_id || ! in_array( $reaction, array( 'like', 'love', 'laugh', 'wow', 'sad', 'angry' ) ) ) {
        wp_send_json_error( array( 'message' => 'Invalid reaction' ) );
    }
    
    $user_ip = $_SERVER['REMOTE_ADDR'];
    $user_reactions = get_post_meta( $post_id, '_pulsepress_user_reactions', true );
    if ( ! is_array( $user_reactions ) ) {
        $user_reactions = array();
    }
    
    // Check if user already reacted
    $user_key = md5( $user_ip );
    if ( isset( $user_reactions[ $user_key ] ) ) {
        $old_reaction = $user_reactions[ $user_key ];
        // Remove old reaction count
        $old_count = get_post_meta( $post_id, '_pulsepress_reaction_' . $old_reaction, true );
        update_post_meta( $post_id, '_pulsepress_reaction_' . $old_reaction, max( 0, intval( $old_count ) - 1 ) );
    }
    
    // Add new reaction
    $user_reactions[ $user_key ] = $reaction;
    update_post_meta( $post_id, '_pulsepress_user_reactions', $user_reactions );
    
    $current_count = get_post_meta( $post_id, '_pulsepress_reaction_' . $reaction, true );
    $new_count = intval( $current_count ) + 1;
    update_post_meta( $post_id, '_pulsepress_reaction_' . $reaction, $new_count );
    
    // Get all reaction counts
    $reactions = array();
    foreach ( array( 'like', 'love', 'laugh', 'wow', 'sad', 'angry' ) as $r ) {
        $reactions[ $r ] = intval( get_post_meta( $post_id, '_pulsepress_reaction_' . $r, true ) );
    }
    
    wp_send_json_success( array(
        'reactions'     => $reactions,
        'user_reaction' => $reaction,
    ) );
}

/**
 * Add reading progress bar
 */
function pulsepress_add_reading_progress_bar() {
    if ( is_single() && get_theme_mod( 'pulsepress_reading_progress_bar', true ) ) {
        ?>
        <div id="reading-progress-bar" class="reading-progress-bar">
            <div class="reading-progress-fill"></div>
        </div>
        <?php
    }
}

/**
 * Add reading time and reactions to content
 */
function pulsepress_add_reading_time_and_reactions( $content ) {
    if ( ! is_single() || ! is_main_query() ) {
        return $content;
    }
    
    $post_id = get_the_ID();
    $reading_time = get_post_meta( $post_id, '_pulsepress_reading_time', true );
    
    $additions = '';
    
    // Reading time
    if ( $reading_time && get_theme_mod( 'pulsepress_show_reading_time', true ) ) {
        $additions .= '<div class="reading-time">';
        $additions .= '<i class="fas fa-clock"></i> ';
        $additions .= sprintf( esc_html__( '%d min read', 'pulsepress' ), $reading_time );
        $additions .= '</div>';
    }
    
    // Post reactions
    if ( get_theme_mod( 'pulsepress_post_reactions', true ) ) {
        $additions .= pulsepress_get_post_reactions_html( $post_id );
    }
    
    return $content . $additions;
}

/**
 * Get post reactions HTML
 */
function pulsepress_get_post_reactions_html( $post_id ) {
    $reactions = array(
        'like'  => array( 'icon' => '👍', 'label' => __( 'Like', 'pulsepress' ) ),
        'love'  => array( 'icon' => '❤️', 'label' => __( 'Love', 'pulsepress' ) ),
        'laugh' => array( 'icon' => '😂', 'label' => __( 'Laugh', 'pulsepress' ) ),
        'wow'   => array( 'icon' => '😮', 'label' => __( 'Wow', 'pulsepress' ) ),
        'sad'   => array( 'icon' => '😢', 'label' => __( 'Sad', 'pulsepress' ) ),
        'angry' => array( 'icon' => '😠', 'label' => __( 'Angry', 'pulsepress' ) ),
    );
    
    $user_ip = $_SERVER['REMOTE_ADDR'];
    $user_reactions = get_post_meta( $post_id, '_pulsepress_user_reactions', true );
    $user_key = md5( $user_ip );
    $user_reaction = isset( $user_reactions[ $user_key ] ) ? $user_reactions[ $user_key ] : '';
    
    $html = '<div class="post-reactions" data-post-id="' . esc_attr( $post_id ) . '">';
    $html .= '<h4>' . esc_html__( 'How did this make you feel?', 'pulsepress' ) . '</h4>';
    $html .= '<div class="reaction-buttons">';
    
    foreach ( $reactions as $key => $reaction ) {
        $count = intval( get_post_meta( $post_id, '_pulsepress_reaction_' . $key, true ) );
        $active = $user_reaction === $key ? ' active' : '';
        
        $html .= '<button class="reaction-btn' . $active . '" data-reaction="' . esc_attr( $key ) . '">';
        $html .= '<span class="reaction-icon">' . $reaction['icon'] . '</span>';
        $html .= '<span class="reaction-label">' . esc_html( $reaction['label'] ) . '</span>';
        $html .= '<span class="reaction-count">' . $count . '</span>';
        $html .= '</button>';
    }
    
    $html .= '</div>';
    $html .= '</div>';
    
    return $html;
}

/**
 * Infinite scroll shortcode
 */
function pulsepress_infinite_scroll_shortcode( $atts ) {
    $atts = shortcode_atts( array(
        'posts_per_page' => 6,
        'category'       => '',
        'auto_load'      => 'true',
    ), $atts );
    
    $args = array(
        'post_type'      => 'post',
        'post_status'    => 'publish',
        'posts_per_page' => intval( $atts['posts_per_page'] ),
        'paged'          => 1,
    );
    
    if ( ! empty( $atts['category'] ) ) {
        $args['category_name'] = $atts['category'];
    }
    
    $query = new WP_Query( $args );
    
    if ( ! $query->have_posts() ) {
        return '<p>' . esc_html__( 'No posts found.', 'pulsepress' ) . '</p>';
    }
    
    ob_start();
    ?>
    <div class="infinite-scroll-container" 
         data-posts-per-page="<?php echo esc_attr( $atts['posts_per_page'] ); ?>"
         data-category="<?php echo esc_attr( $atts['category'] ); ?>"
         data-auto-load="<?php echo esc_attr( $atts['auto_load'] ); ?>">
        
        <div class="posts-grid">
            <?php
            while ( $query->have_posts() ) {
                $query->the_post();
                get_template_part( 'template-parts/content/content', 'excerpt' );
            }
            wp_reset_postdata();
            ?>
        </div>
        
        <?php if ( $query->max_num_pages > 1 ) : ?>
            <div class="load-more-container">
                <button class="load-more-btn" data-page="2">
                    <?php esc_html_e( 'Load More', 'pulsepress' ); ?>
                </button>
                <div class="loading-spinner" style="display: none;">
                    <i class="fas fa-spinner fa-spin"></i>
                </div>
            </div>
        <?php endif; ?>
    </div>
    <?php
    
    return ob_get_clean();
}

/**
 * Add engagement settings to customizer
 */
function pulsepress_engagement_customizer( $wp_customize ) {
    // Engagement Section
    $wp_customize->add_section(
        'pulsepress_engagement',
        array(
            'title'    => esc_html__( 'User Engagement', 'pulsepress' ),
            'priority' => 180,
        )
    );
    
    // Reading Progress Bar
    $wp_customize->add_setting(
        'pulsepress_reading_progress_bar',
        array(
            'default'           => true,
            'sanitize_callback' => 'pulsepress_sanitize_checkbox',
        )
    );
    
    $wp_customize->add_control(
        'pulsepress_reading_progress_bar',
        array(
            'label'   => esc_html__( 'Show Reading Progress Bar', 'pulsepress' ),
            'section' => 'pulsepress_engagement',
            'type'    => 'checkbox',
        )
    );
    
    // Show Reading Time
    $wp_customize->add_setting(
        'pulsepress_show_reading_time',
        array(
            'default'           => true,
            'sanitize_callback' => 'pulsepress_sanitize_checkbox',
        )
    );
    
    $wp_customize->add_control(
        'pulsepress_show_reading_time',
        array(
            'label'   => esc_html__( 'Show Reading Time', 'pulsepress' ),
            'section' => 'pulsepress_engagement',
            'type'    => 'checkbox',
        )
    );
    
    // Post Reactions
    $wp_customize->add_setting(
        'pulsepress_post_reactions',
        array(
            'default'           => true,
            'sanitize_callback' => 'pulsepress_sanitize_checkbox',
        )
    );
    
    $wp_customize->add_control(
        'pulsepress_post_reactions',
        array(
            'label'   => esc_html__( 'Enable Post Reactions', 'pulsepress' ),
            'section' => 'pulsepress_engagement',
            'type'    => 'checkbox',
        )
    );
    
    // Infinite Scroll
    $wp_customize->add_setting(
        'pulsepress_infinite_scroll',
        array(
            'default'           => false,
            'sanitize_callback' => 'pulsepress_sanitize_checkbox',
        )
    );
    
    $wp_customize->add_control(
        'pulsepress_infinite_scroll',
        array(
            'label'       => esc_html__( 'Enable Infinite Scroll', 'pulsepress' ),
            'description' => esc_html__( 'Automatically load more posts when scrolling', 'pulsepress' ),
            'section'     => 'pulsepress_engagement',
            'type'        => 'checkbox',
        )
    );
}
add_action( 'customize_register', 'pulsepress_engagement_customizer' );
