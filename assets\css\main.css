/*
 * PulsePress Theme - Main Stylesheet
 * A responsive WordPress news theme
 */

/* ---------------
   1. Variables
--------------- */
:root {
  /* Colors */
  --primary-color: #1e73be;
  --secondary-color: #ff6b6b;
  --accent-color: #ffd166;
  --dark-color: #2c3e50;
  --light-color: #f8f9fa;
  --gray-color: #6c757d;
  --success-color: #28a745;
  --warning-color: #ffc107;
  --danger-color: #dc3545;
  --info-color: #17a2b8;

  /* Typography */
  --body-font: 'Roboto', sans-serif;
  --heading-font: 'Montserrat', sans-serif;
  --base-font-size: 16px;
  --h1-size: 2.5rem;
  --h2-size: 2rem;
  --h3-size: 1.75rem;
  --h4-size: 1.5rem;
  --h5-size: 1.25rem;
  --h6-size: 1rem;

  /* Spacing */
  --spacing-xs: 0.25rem;
  --spacing-sm: 0.5rem;
  --spacing-md: 1rem;
  --spacing-lg: 1.5rem;
  --spacing-xl: 3rem;

  /* Container widths */
  --container-sm: 540px;
  --container-md: 720px;
  --container-lg: 960px;
  --container-xl: 1140px;

  /* Transitions */
  --transition-speed: 0.3s;

  /* Border radius */
  --border-radius: 0.25rem;

  /* Box shadow */
  --box-shadow: 0 0.5rem 1rem rgba(0, 0, 0, 0.15);
}

/* Dark mode variables */
[data-theme="dark"] {
  --primary-color: #3498db;
  --secondary-color: #ff8787;
  --accent-color: #ffe066;
  --dark-color: #121212;
  --light-color: #1e1e1e;
  --gray-color: #adb5bd;
  --text-color: #f8f9fa;
  --bg-color: #121212;
  --card-bg: #1e1e1e;
}

/* ---------------
   2. Reset & Base
--------------- */
*, *::before, *::after {
  box-sizing: border-box;
}

html {
  font-size: var(--base-font-size);
  scroll-behavior: smooth;
}

body {
  font-family: var(--body-font);
  line-height: 1.6;
  color: var(--dark-color);
  background-color: var(--light-color);
  margin: 0;
  padding: 0;
  transition: background-color var(--transition-speed) ease;
}

[data-theme="dark"] body {
  color: var(--text-color);
  background-color: var(--bg-color);
}

h1, h2, h3, h4, h5, h6 {
  font-family: var(--heading-font);
  font-weight: 700;
  line-height: 1.2;
  margin-top: 0;
  margin-bottom: var(--spacing-md);
}

h1 { font-size: var(--h1-size); }
h2 { font-size: var(--h2-size); }
h3 { font-size: var(--h3-size); }
h4 { font-size: var(--h4-size); }
h5 { font-size: var(--h5-size); }
h6 { font-size: var(--h6-size); }

p {
  margin-top: 0;
  margin-bottom: var(--spacing-md);
}

a {
  color: var(--primary-color);
  text-decoration: none;
  transition: color var(--transition-speed) ease;
}

a:hover {
  color: darken(var(--primary-color), 10%);
}

img {
  max-width: 100%;
  height: auto;
}

/* ---------------
   3. Layout
--------------- */
.container {
  width: 100%;
  padding-right: var(--spacing-md);
  padding-left: var(--spacing-md);
  margin-right: auto;
  margin-left: auto;
}

@media (min-width: 576px) {
  .container {
    max-width: var(--container-sm);
  }
}

@media (min-width: 768px) {
  .container {
    max-width: var(--container-md);
  }
}

@media (min-width: 992px) {
  .container {
    max-width: var(--container-lg);
  }
}

@media (min-width: 1200px) {
  .container {
    max-width: var(--container-xl);
  }
}

.row {
  display: flex;
  flex-wrap: wrap;
  margin-right: -15px;
  margin-left: -15px;
}

.col {
  position: relative;
  width: 100%;
  padding-right: 15px;
  padding-left: 15px;
}

/* Grid system */
.col-1 { flex: 0 0 8.333333%; max-width: 8.333333%; }
.col-2 { flex: 0 0 16.666667%; max-width: 16.666667%; }
.col-3 { flex: 0 0 25%; max-width: 25%; }
.col-4 { flex: 0 0 33.333333%; max-width: 33.333333%; }
.col-5 { flex: 0 0 41.666667%; max-width: 41.666667%; }
.col-6 { flex: 0 0 50%; max-width: 50%; }
.col-7 { flex: 0 0 58.333333%; max-width: 58.333333%; }
.col-8 { flex: 0 0 66.666667%; max-width: 66.666667%; }
.col-9 { flex: 0 0 75%; max-width: 75%; }
.col-10 { flex: 0 0 83.333333%; max-width: 83.333333%; }
.col-11 { flex: 0 0 91.666667%; max-width: 91.666667%; }
.col-12 { flex: 0 0 100%; max-width: 100%; }

@media (min-width: 576px) {
  .col-sm-1 { flex: 0 0 8.333333%; max-width: 8.333333%; }
  .col-sm-2 { flex: 0 0 16.666667%; max-width: 16.666667%; }
  .col-sm-3 { flex: 0 0 25%; max-width: 25%; }
  .col-sm-4 { flex: 0 0 33.333333%; max-width: 33.333333%; }
  .col-sm-5 { flex: 0 0 41.666667%; max-width: 41.666667%; }
  .col-sm-6 { flex: 0 0 50%; max-width: 50%; }
  .col-sm-7 { flex: 0 0 58.333333%; max-width: 58.333333%; }
  .col-sm-8 { flex: 0 0 66.666667%; max-width: 66.666667%; }
  .col-sm-9 { flex: 0 0 75%; max-width: 75%; }
  .col-sm-10 { flex: 0 0 83.333333%; max-width: 83.333333%; }
  .col-sm-11 { flex: 0 0 91.666667%; max-width: 91.666667%; }
  .col-sm-12 { flex: 0 0 100%; max-width: 100%; }
}

@media (min-width: 768px) {
  .col-md-1 { flex: 0 0 8.333333%; max-width: 8.333333%; }
  .col-md-2 { flex: 0 0 16.666667%; max-width: 16.666667%; }
  .col-md-3 { flex: 0 0 25%; max-width: 25%; }
  .col-md-4 { flex: 0 0 33.333333%; max-width: 33.333333%; }
  .col-md-5 { flex: 0 0 41.666667%; max-width: 41.666667%; }
  .col-md-6 { flex: 0 0 50%; max-width: 50%; }
  .col-md-7 { flex: 0 0 58.333333%; max-width: 58.333333%; }
  .col-md-8 { flex: 0 0 66.666667%; max-width: 66.666667%; }
  .col-md-9 { flex: 0 0 75%; max-width: 75%; }
  .col-md-10 { flex: 0 0 83.333333%; max-width: 83.333333%; }
  .col-md-11 { flex: 0 0 91.666667%; max-width: 91.666667%; }
  .col-md-12 { flex: 0 0 100%; max-width: 100%; }
}

@media (min-width: 992px) {
  .col-lg-1 { flex: 0 0 8.333333%; max-width: 8.333333%; }
  .col-lg-2 { flex: 0 0 16.666667%; max-width: 16.666667%; }
  .col-lg-3 { flex: 0 0 25%; max-width: 25%; }
  .col-lg-4 { flex: 0 0 33.333333%; max-width: 33.333333%; }
  .col-lg-5 { flex: 0 0 41.666667%; max-width: 41.666667%; }
  .col-lg-6 { flex: 0 0 50%; max-width: 50%; }
  .col-lg-7 { flex: 0 0 58.333333%; max-width: 58.333333%; }
  .col-lg-8 { flex: 0 0 66.666667%; max-width: 66.666667%; }
  .col-lg-9 { flex: 0 0 75%; max-width: 75%; }
  .col-lg-10 { flex: 0 0 83.333333%; max-width: 83.333333%; }
  .col-lg-11 { flex: 0 0 91.666667%; max-width: 91.666667%; }
  .col-lg-12 { flex: 0 0 100%; max-width: 100%; }
}

/* ---------------
   4. Header
--------------- */
.site-header {
  background-color: #fff;
  box-shadow: var(--box-shadow);
  position: sticky;
  top: 0;
  z-index: 1000;
  transition: all var(--transition-speed) ease;
}

[data-theme="dark"] .site-header {
  background-color: var(--dark-color);
}

.header-top {
  background-color: var(--primary-color);
  color: white;
  padding: var(--spacing-xs) 0;
}

.trending-ticker {
  overflow: hidden;
  white-space: nowrap;
}

.trending-ticker-label {
  display: inline-block;
  background-color: var(--secondary-color);
  color: white;
  padding: 0 var(--spacing-sm);
  margin-right: var(--spacing-sm);
  font-weight: bold;
  border-radius: var(--border-radius);
}

.trending-ticker-content {
  display: inline-block;
  animation: ticker 20s linear infinite;
}

@keyframes ticker {
  0% { transform: translateX(100%); }
  100% { transform: translateX(-100%); }
}

.trending-ticker-item {
  display: inline-block;
  margin-right: var(--spacing-lg);
}

.trending-ticker-item a {
  color: white;
}

.header-main {
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding: var(--spacing-md) 0;
}

.site-branding {
  display: flex;
  align-items: center;
}

.site-logo {
  max-height: 60px;
  width: auto;
  margin-right: var(--spacing-sm);
}

.site-title {
  font-size: 1.5rem;
  margin: 0;
}

.site-description {
  margin: 0;
  font-size: 0.875rem;
  color: var(--gray-color);
}

.header-search {
  position: relative;
}

.search-form {
  display: flex;
}

.search-field {
  border: 1px solid var(--gray-color);
  border-radius: var(--border-radius);
  padding: var(--spacing-xs) var(--spacing-sm);
  font-size: 0.875rem;
}

.search-submit {
  background-color: var(--primary-color);
  color: white;
  border: none;
  border-radius: 0 var(--border-radius) var(--border-radius) 0;
  padding: var(--spacing-xs) var(--spacing-sm);
  cursor: pointer;
}

/* Main Navigation */
.main-navigation {
  background-color: var(--dark-color);
}

.menu-toggle {
  display: none;
}

.main-menu {
  display: flex;
  list-style: none;
  margin: 0;
  padding: 0;
}

.main-menu > li {
  position: relative;
}

.main-menu > li > a {
  display: block;
  color: white;
  padding: var(--spacing-sm) var(--spacing-md);
  font-weight: 500;
}

.main-menu > li:hover > a,
.main-menu > li.current-menu-item > a {
  background-color: var(--primary-color);
}

/* Dropdown Menu */
.sub-menu {
  position: absolute;
  top: 100%;
  left: 0;
  background-color: white;
  box-shadow: var(--box-shadow);
  min-width: 200px;
  list-style: none;
  margin: 0;
  padding: 0;
  opacity: 0;
  visibility: hidden;
  transform: translateY(10px);
  transition: all var(--transition-speed) ease;
  z-index: 100;
}

[data-theme="dark"] .sub-menu {
  background-color: var(--dark-color);
}

.main-menu > li:hover > .sub-menu {
  opacity: 1;
  visibility: visible;
  transform: translateY(0);
}

.sub-menu li {
  border-bottom: 1px solid rgba(0,0,0,0.1);
}

.sub-menu li:last-child {
  border-bottom: none;
}

.sub-menu a {
  display: block;
  padding: var(--spacing-sm) var(--spacing-md);
  color: var(--dark-color);
}

[data-theme="dark"] .sub-menu a {
  color: white;
}

.sub-menu a:hover {
  background-color: rgba(0,0,0,0.05);
}

[data-theme="dark"] .sub-menu a:hover {
  background-color: rgba(255,255,255,0.05);
}

/* Mega Menu */
.mega-menu {
  position: absolute;
  top: 100%;
  left: 0;
  width: 100%;
  background-color: white;
  box-shadow: var(--box-shadow);
  padding: var(--spacing-md);
  opacity: 0;
  visibility: hidden;
  transform: translateY(10px);
  transition: all var(--transition-speed) ease;
  z-index: 100;
  display: flex;
  flex-wrap: wrap;
}

[data-theme="dark"] .mega-menu {
  background-color: var(--dark-color);
}

.main-menu > li.has-mega-menu {
  position: static;
}

.main-menu > li.has-mega-menu:hover .mega-menu {
  opacity: 1;
  visibility: visible;
  transform: translateY(0);
}

.mega-menu-column {
  flex: 1;
  padding: 0 var(--spacing-md);
}

.mega-menu-title {
  font-weight: bold;
  margin-bottom: var(--spacing-sm);
  color: var(--primary-color);
  border-bottom: 1px solid var(--gray-color);
  padding-bottom: var(--spacing-xs);
}

.mega-menu-list {
  list-style: none;
  margin: 0;
  padding: 0;
}

.mega-menu-list li {
  margin-bottom: var(--spacing-xs);
}

.mega-menu-list a {
  color: var(--dark-color);
}

[data-theme="dark"] .mega-menu-list a {
  color: white;
}

/* Mobile Menu */
@media (max-width: 991px) {
  .menu-toggle {
    display: block;
    background: none;
    border: none;
    color: white;
    font-size: 1.5rem;
    cursor: pointer;
    padding: var(--spacing-sm);
  }

  .main-menu {
    display: none;
    flex-direction: column;
    width: 100%;
  }

  .main-menu.active {
    display: flex;
  }

  .main-menu > li {
    width: 100%;
  }

  .sub-menu,
  .mega-menu {
    position: static;
    opacity: 1;
    visibility: visible;
    transform: none;
    box-shadow: none;
    width: 100%;
    display: none;
  }

  .main-menu > li.menu-item-has-children > a::after {
    content: "+";
    margin-left: var(--spacing-sm);
  }

  .main-menu > li.menu-item-has-children.active > a::after {
    content: "-";
  }

  .main-menu > li.menu-item-has-children.active > .sub-menu,
  .main-menu > li.has-mega-menu.active > .mega-menu {
    display: block;
  }

  .mega-menu {
    flex-direction: column;
  }

  .mega-menu-column {
    width: 100%;
    margin-bottom: var(--spacing-md);
  }
}
