<?php
/**
 * Progressive Web App (PWA) Features
 *
 * @package PulsePress
 */

// Prevent direct access
if ( ! defined( 'ABSPATH' ) ) {
    exit;
}

/**
 * Initialize PWA features
 */
function pulsepress_init_pwa() {
    add_action( 'wp_head', 'pulsepress_add_pwa_meta_tags' );
    add_action( 'wp_head', 'pulsepress_add_manifest_link' );
    add_action( 'wp_footer', 'pulsepress_add_service_worker_registration' );
    add_action( 'init', 'pulsepress_create_pwa_endpoints' );
    add_action( 'template_redirect', 'pulsepress_serve_pwa_files' );
}
add_action( 'init', 'pulsepress_init_pwa' );

/**
 * Add PWA meta tags
 */
function pulsepress_add_pwa_meta_tags() {
    if ( ! get_theme_mod( 'pulsepress_enable_pwa', false ) ) {
        return;
    }
    
    // Theme color
    $theme_color = get_theme_mod( 'pulsepress_primary_color', '#1e73be' );
    echo '<meta name="theme-color" content="' . esc_attr( $theme_color ) . '">' . "\n";
    
    // Mobile app capable
    echo '<meta name="mobile-web-app-capable" content="yes">' . "\n";
    echo '<meta name="apple-mobile-web-app-capable" content="yes">' . "\n";
    echo '<meta name="apple-mobile-web-app-status-bar-style" content="default">' . "\n";
    echo '<meta name="apple-mobile-web-app-title" content="' . esc_attr( get_bloginfo( 'name' ) ) . '">' . "\n";
    
    // App icons
    $icon_192 = get_theme_mod( 'pulsepress_pwa_icon_192', '' );
    $icon_512 = get_theme_mod( 'pulsepress_pwa_icon_512', '' );
    
    if ( $icon_192 ) {
        echo '<link rel="icon" type="image/png" sizes="192x192" href="' . esc_url( $icon_192 ) . '">' . "\n";
        echo '<link rel="apple-touch-icon" sizes="192x192" href="' . esc_url( $icon_192 ) . '">' . "\n";
    }
    
    if ( $icon_512 ) {
        echo '<link rel="icon" type="image/png" sizes="512x512" href="' . esc_url( $icon_512 ) . '">' . "\n";
        echo '<link rel="apple-touch-icon" sizes="512x512" href="' . esc_url( $icon_512 ) . '">' . "\n";
    }
}

/**
 * Add manifest link
 */
function pulsepress_add_manifest_link() {
    if ( ! get_theme_mod( 'pulsepress_enable_pwa', false ) ) {
        return;
    }
    
    echo '<link rel="manifest" href="' . esc_url( home_url( '/manifest.json' ) ) . '">' . "\n";
}

/**
 * Add service worker registration
 */
function pulsepress_add_service_worker_registration() {
    if ( ! get_theme_mod( 'pulsepress_enable_pwa', false ) ) {
        return;
    }

    ?>
    <script>
    if ('serviceWorker' in navigator) {
        window.addEventListener('load', function() {
            // Register our advanced service worker
            navigator.serviceWorker.register('<?php echo esc_url( get_template_directory_uri() . '/assets/js/sw.js' ); ?>')
                .then(function(registration) {
                    console.log('PulsePress ServiceWorker registration successful');

                    // Check for updates
                    registration.addEventListener('updatefound', function() {
                        const newWorker = registration.installing;
                        newWorker.addEventListener('statechange', function() {
                            if (newWorker.state === 'installed' && navigator.serviceWorker.controller) {
                                // New content is available, prompt user to refresh
                                if (confirm('New content is available! Refresh to update?')) {
                                    window.location.reload();
                                }
                            }
                        });
                    });
                })
                .catch(function(error) {
                    console.log('ServiceWorker registration failed: ', error);
                });
        });

        // Listen for messages from service worker
        navigator.serviceWorker.addEventListener('message', function(event) {
            if (event.data && event.data.type === 'CACHE_UPDATED') {
                console.log('Cache updated by service worker');
            }
        });
    }
    </script>
    <?php
}

/**
 * Create PWA endpoints
 */
function pulsepress_create_pwa_endpoints() {
    add_rewrite_rule( '^manifest\.json$', 'index.php?pulsepress_manifest=1', 'top' );
    add_rewrite_rule( '^sw\.js$', 'index.php?pulsepress_sw=1', 'top' );
    
    // Add query vars
    add_filter( 'query_vars', function( $vars ) {
        $vars[] = 'pulsepress_manifest';
        $vars[] = 'pulsepress_sw';
        return $vars;
    });
}

/**
 * Serve PWA files
 */
function pulsepress_serve_pwa_files() {
    if ( get_query_var( 'pulsepress_manifest' ) ) {
        pulsepress_serve_manifest();
        exit;
    }
    
    if ( get_query_var( 'pulsepress_sw' ) ) {
        pulsepress_serve_service_worker();
        exit;
    }
}

/**
 * Serve manifest.json
 */
function pulsepress_serve_manifest() {
    header( 'Content-Type: application/json' );
    
    $manifest = array(
        'name'             => get_bloginfo( 'name' ),
        'short_name'       => get_theme_mod( 'pulsepress_pwa_short_name', get_bloginfo( 'name' ) ),
        'description'      => get_bloginfo( 'description' ),
        'start_url'        => home_url( '/' ),
        'display'          => 'standalone',
        'background_color' => get_theme_mod( 'pulsepress_pwa_bg_color', '#ffffff' ),
        'theme_color'      => get_theme_mod( 'pulsepress_primary_color', '#1e73be' ),
        'orientation'      => 'portrait-primary',
        'scope'            => home_url( '/' ),
        'lang'             => get_locale(),
        'dir'              => is_rtl() ? 'rtl' : 'ltr',
        'categories'       => array( 'news', 'magazines' ),
        'icons'            => array(),
    );
    
    // Add icons
    $icon_192 = get_theme_mod( 'pulsepress_pwa_icon_192', '' );
    $icon_512 = get_theme_mod( 'pulsepress_pwa_icon_512', '' );
    
    if ( $icon_192 ) {
        $manifest['icons'][] = array(
            'src'     => $icon_192,
            'sizes'   => '192x192',
            'type'    => 'image/png',
            'purpose' => 'any maskable',
        );
    }
    
    if ( $icon_512 ) {
        $manifest['icons'][] = array(
            'src'     => $icon_512,
            'sizes'   => '512x512',
            'type'    => 'image/png',
            'purpose' => 'any maskable',
        );
    }
    
    // Add shortcuts
    $manifest['shortcuts'] = array(
        array(
            'name'        => esc_html__( 'Latest News', 'pulsepress' ),
            'short_name'  => esc_html__( 'Latest', 'pulsepress' ),
            'description' => esc_html__( 'View latest news articles', 'pulsepress' ),
            'url'         => home_url( '/' ),
            'icons'       => array(
                array(
                    'src'   => $icon_192 ?: '',
                    'sizes' => '192x192',
                ),
            ),
        ),
    );
    
    echo wp_json_encode( $manifest, JSON_PRETTY_PRINT | JSON_UNESCAPED_SLASHES );
}

/**
 * Serve service worker
 */
function pulsepress_serve_service_worker() {
    header( 'Content-Type: application/javascript' );
    header( 'Service-Worker-Allowed: /' );
    
    $cache_version = 'pulsepress-v' . PULSEPRESS_VERSION;
    $cache_files = array(
        home_url( '/' ),
        get_stylesheet_uri(),
        get_template_directory_uri() . '/assets/js/main.js',
        get_template_directory_uri() . '/assets/js/engagement.js',
        get_template_directory_uri() . '/assets/css/engagement.css',
    );
    
    ?>
const CACHE_NAME = '<?php echo esc_js( $cache_version ); ?>';
const urlsToCache = <?php echo wp_json_encode( $cache_files ); ?>;

// Install event
self.addEventListener('install', function(event) {
    event.waitUntil(
        caches.open(CACHE_NAME)
            .then(function(cache) {
                return cache.addAll(urlsToCache);
            })
    );
});

// Fetch event
self.addEventListener('fetch', function(event) {
    event.respondWith(
        caches.match(event.request)
            .then(function(response) {
                // Return cached version or fetch from network
                if (response) {
                    return response;
                }
                
                return fetch(event.request).then(function(response) {
                    // Don't cache non-successful responses
                    if (!response || response.status !== 200 || response.type !== 'basic') {
                        return response;
                    }
                    
                    // Clone the response
                    var responseToCache = response.clone();
                    
                    caches.open(CACHE_NAME)
                        .then(function(cache) {
                            cache.put(event.request, responseToCache);
                        });
                    
                    return response;
                });
            })
    );
});

// Activate event
self.addEventListener('activate', function(event) {
    event.waitUntil(
        caches.keys().then(function(cacheNames) {
            return Promise.all(
                cacheNames.map(function(cacheName) {
                    if (cacheName !== CACHE_NAME) {
                        return caches.delete(cacheName);
                    }
                })
            );
        })
    );
});

// Background sync for offline form submissions
self.addEventListener('sync', function(event) {
    if (event.tag === 'background-sync') {
        event.waitUntil(doBackgroundSync());
    }
});

function doBackgroundSync() {
    // Handle offline form submissions, comments, etc.
    return new Promise(function(resolve) {
        // Implementation for background sync
        resolve();
    });
}

// Push notifications
self.addEventListener('push', function(event) {
    if (event.data) {
        const data = event.data.json();
        const options = {
            body: data.body,
            icon: data.icon || '/wp-content/themes/pulsepress/assets/images/icon-192x192.png',
            badge: '/wp-content/themes/pulsepress/assets/images/badge-72x72.png',
            vibrate: [100, 50, 100],
            data: {
                dateOfArrival: Date.now(),
                primaryKey: data.primaryKey
            },
            actions: [
                {
                    action: 'explore',
                    title: 'Read Article',
                    icon: '/wp-content/themes/pulsepress/assets/images/checkmark.png'
                },
                {
                    action: 'close',
                    title: 'Close',
                    icon: '/wp-content/themes/pulsepress/assets/images/xmark.png'
                }
            ]
        };
        
        event.waitUntil(
            self.registration.showNotification(data.title, options)
        );
    }
});

// Notification click
self.addEventListener('notificationclick', function(event) {
    event.notification.close();
    
    if (event.action === 'explore') {
        event.waitUntil(
            clients.openWindow(event.notification.data.url || '/')
        );
    }
});
    <?php
}

/**
 * Add PWA settings to customizer
 */
function pulsepress_pwa_customizer( $wp_customize ) {
    // PWA Section
    $wp_customize->add_section(
        'pulsepress_pwa',
        array(
            'title'    => esc_html__( 'PWA Settings', 'pulsepress' ),
            'priority' => 200,
        )
    );
    
    // Enable PWA
    $wp_customize->add_setting(
        'pulsepress_enable_pwa',
        array(
            'default'           => false,
            'sanitize_callback' => 'pulsepress_sanitize_checkbox',
        )
    );
    
    $wp_customize->add_control(
        'pulsepress_enable_pwa',
        array(
            'label'       => esc_html__( 'Enable PWA', 'pulsepress' ),
            'description' => esc_html__( 'Make your site installable as a Progressive Web App', 'pulsepress' ),
            'section'     => 'pulsepress_pwa',
            'type'        => 'checkbox',
        )
    );
    
    // PWA Short Name
    $wp_customize->add_setting(
        'pulsepress_pwa_short_name',
        array(
            'default'           => get_bloginfo( 'name' ),
            'sanitize_callback' => 'sanitize_text_field',
        )
    );
    
    $wp_customize->add_control(
        'pulsepress_pwa_short_name',
        array(
            'label'       => esc_html__( 'App Short Name', 'pulsepress' ),
            'description' => esc_html__( 'Short name for the app (12 characters max)', 'pulsepress' ),
            'section'     => 'pulsepress_pwa',
            'type'        => 'text',
        )
    );
    
    // PWA Background Color
    $wp_customize->add_setting(
        'pulsepress_pwa_bg_color',
        array(
            'default'           => '#ffffff',
            'sanitize_callback' => 'sanitize_hex_color',
        )
    );
    
    $wp_customize->add_control(
        new WP_Customize_Color_Control(
            $wp_customize,
            'pulsepress_pwa_bg_color',
            array(
                'label'   => esc_html__( 'App Background Color', 'pulsepress' ),
                'section' => 'pulsepress_pwa',
            )
        )
    );
    
    // PWA Icon 192x192
    $wp_customize->add_setting(
        'pulsepress_pwa_icon_192',
        array(
            'default'           => '',
            'sanitize_callback' => 'esc_url_raw',
        )
    );
    
    $wp_customize->add_control(
        new WP_Customize_Image_Control(
            $wp_customize,
            'pulsepress_pwa_icon_192',
            array(
                'label'       => esc_html__( 'App Icon 192x192', 'pulsepress' ),
                'description' => esc_html__( 'Upload a 192x192 PNG icon', 'pulsepress' ),
                'section'     => 'pulsepress_pwa',
            )
        )
    );
    
    // PWA Icon 512x512
    $wp_customize->add_setting(
        'pulsepress_pwa_icon_512',
        array(
            'default'           => '',
            'sanitize_callback' => 'esc_url_raw',
        )
    );
    
    $wp_customize->add_control(
        new WP_Customize_Image_Control(
            $wp_customize,
            'pulsepress_pwa_icon_512',
            array(
                'label'       => esc_html__( 'App Icon 512x512', 'pulsepress' ),
                'description' => esc_html__( 'Upload a 512x512 PNG icon', 'pulsepress' ),
                'section'     => 'pulsepress_pwa',
            )
        )
    );
}
add_action( 'customize_register', 'pulsepress_pwa_customizer' );

/**
 * Flush rewrite rules on theme activation
 */
function pulsepress_pwa_flush_rewrite_rules() {
    pulsepress_create_pwa_endpoints();
    flush_rewrite_rules();
}
register_activation_hook( __FILE__, 'pulsepress_pwa_flush_rewrite_rules' );
