/**
 * PulsePress Engagement Features
 * 
 * Handles infinite scroll, reading progress, and post reactions
 */

(function($) {
    'use strict';

    // Initialize engagement features
    $(document).ready(function() {
        initReadingProgress();
        initInfiniteScroll();
        initPostReactions();
        initEstimatedReadTime();
    });

    /**
     * Reading progress bar
     */
    function initReadingProgress() {
        const progressBar = $('#reading-progress-bar');
        if (progressBar.length === 0) return;

        const progressFill = progressBar.find('.reading-progress-fill');
        const article = $('article.post, .entry-content, .post-content').first();
        
        if (article.length === 0) return;

        $(window).on('scroll', function() {
            const windowTop = $(window).scrollTop();
            const articleTop = article.offset().top;
            const articleHeight = article.outerHeight();
            const windowHeight = $(window).height();
            
            // Calculate progress
            const progress = Math.min(100, Math.max(0, 
                ((windowTop - articleTop + windowHeight) / articleHeight) * 100
            ));
            
            progressFill.css('width', progress + '%');
            
            // Show/hide progress bar based on scroll position
            if (windowTop > articleTop) {
                progressBar.addClass('visible');
            } else {
                progressBar.removeClass('visible');
            }
        });
    }

    /**
     * Infinite scroll functionality
     */
    function initInfiniteScroll() {
        const container = $('.infinite-scroll-container');
        if (container.length === 0) return;

        const loadMoreBtn = container.find('.load-more-btn');
        const loadingSpinner = container.find('.loading-spinner');
        const postsGrid = container.find('.posts-grid');
        
        let isLoading = false;
        let currentPage = parseInt(loadMoreBtn.data('page')) || 2;
        let hasMore = true;
        
        const postsPerPage = container.data('posts-per-page') || 6;
        const category = container.data('category') || '';
        const autoLoad = container.data('auto-load') === 'true';

        // Load more button click
        loadMoreBtn.on('click', function(e) {
            e.preventDefault();
            loadMorePosts();
        });

        // Auto-load on scroll
        if (autoLoad) {
            $(window).on('scroll', function() {
                if (isLoading || !hasMore) return;
                
                const scrollTop = $(window).scrollTop();
                const windowHeight = $(window).height();
                const documentHeight = $(document).height();
                
                // Load more when 200px from bottom
                if (scrollTop + windowHeight >= documentHeight - 200) {
                    loadMorePosts();
                }
            });
        }

        function loadMorePosts() {
            if (isLoading || !hasMore) return;
            
            isLoading = true;
            loadMoreBtn.hide();
            loadingSpinner.show();

            $.ajax({
                url: pulsepress_engagement.ajax_url,
                type: 'POST',
                data: {
                    action: 'load_more_posts',
                    page: currentPage,
                    posts_per_page: postsPerPage,
                    category: category,
                    nonce: pulsepress_engagement.nonce
                },
                success: function(response) {
                    if (response.success) {
                        // Append new posts with fade-in animation
                        const newPosts = $(response.data.content).hide();
                        postsGrid.append(newPosts);
                        newPosts.fadeIn(500);
                        
                        currentPage = response.data.next_page;
                        hasMore = response.data.has_more;
                        
                        if (hasMore) {
                            loadMoreBtn.data('page', currentPage).show();
                        } else {
                            loadMoreBtn.text(pulsepress_engagement.strings.no_more);
                        }
                    } else {
                        console.error('Error loading posts:', response.data.message);
                        loadMoreBtn.text(pulsepress_engagement.strings.error);
                    }
                },
                error: function() {
                    console.error('AJAX error loading posts');
                    loadMoreBtn.text(pulsepress_engagement.strings.error);
                },
                complete: function() {
                    isLoading = false;
                    loadingSpinner.hide();
                }
            });
        }
    }

    /**
     * Post reactions
     */
    function initPostReactions() {
        $('.post-reactions').on('click', '.reaction-btn', function(e) {
            e.preventDefault();
            
            const btn = $(this);
            const container = btn.closest('.post-reactions');
            const postId = container.data('post-id');
            const reaction = btn.data('reaction');
            
            if (btn.hasClass('loading')) return;
            
            // Add loading state
            btn.addClass('loading');
            
            $.ajax({
                url: pulsepress_engagement.ajax_url,
                type: 'POST',
                data: {
                    action: 'post_reaction',
                    post_id: postId,
                    reaction: reaction,
                    nonce: pulsepress_engagement.nonce
                },
                success: function(response) {
                    if (response.success) {
                        // Update reaction counts
                        const reactions = response.data.reactions;
                        const userReaction = response.data.user_reaction;
                        
                        container.find('.reaction-btn').each(function() {
                            const reactionBtn = $(this);
                            const reactionType = reactionBtn.data('reaction');
                            const count = reactions[reactionType] || 0;
                            
                            reactionBtn.find('.reaction-count').text(count);
                            
                            // Update active state
                            if (reactionType === userReaction) {
                                reactionBtn.addClass('active');
                            } else {
                                reactionBtn.removeClass('active');
                            }
                        });
                        
                        // Add animation
                        btn.addClass('reacted');
                        setTimeout(() => btn.removeClass('reacted'), 300);
                    } else {
                        console.error('Error submitting reaction:', response.data.message);
                    }
                },
                error: function() {
                    console.error('AJAX error submitting reaction');
                },
                complete: function() {
                    btn.removeClass('loading');
                }
            });
        });
    }

    /**
     * Estimated read time with progress
     */
    function initEstimatedReadTime() {
        const readingTime = $('.reading-time');
        if (readingTime.length === 0) return;

        const article = $('article.post, .entry-content, .post-content').first();
        if (article.length === 0) return;

        // Add progress indicator to reading time
        const progressIndicator = $('<span class="reading-progress-text"></span>');
        readingTime.append(' • ').append(progressIndicator);

        $(window).on('scroll', function() {
            const windowTop = $(window).scrollTop();
            const articleTop = article.offset().top;
            const articleHeight = article.outerHeight();
            const windowHeight = $(window).height();
            
            const progress = Math.min(100, Math.max(0, 
                ((windowTop - articleTop + windowHeight) / articleHeight) * 100
            ));
            
            if (progress > 0 && progress < 100) {
                progressIndicator.text(Math.round(progress) + '% read');
            } else if (progress >= 100) {
                progressIndicator.text('Completed');
            } else {
                progressIndicator.text('');
            }
        });
    }

    /**
     * Smooth scroll to comments
     */
    function initSmoothScrollToComments() {
        $('.comments-link, .comment-count').on('click', function(e) {
            const href = $(this).attr('href');
            if (href && href.indexOf('#') !== -1) {
                e.preventDefault();
                const target = $(href);
                if (target.length) {
                    $('html, body').animate({
                        scrollTop: target.offset().top - 100
                    }, 500);
                }
            }
        });
    }

    /**
     * Auto-refresh trending content
     */
    function initAutoRefresh() {
        const trendingTicker = $('.trending-ticker');
        if (trendingTicker.length === 0) return;

        // Refresh trending content every 5 minutes
        setInterval(function() {
            $.ajax({
                url: pulsepress_engagement.ajax_url,
                type: 'POST',
                data: {
                    action: 'refresh_trending',
                    nonce: pulsepress_engagement.nonce
                },
                success: function(response) {
                    if (response.success && response.data.content) {
                        trendingTicker.fadeOut(300, function() {
                            $(this).html(response.data.content).fadeIn(300);
                        });
                    }
                },
                error: function() {
                    console.log('Failed to refresh trending content');
                }
            });
        }, 300000); // 5 minutes
    }

    /**
     * Live comment updates
     */
    function initLiveComments() {
        const commentsSection = $('#comments');
        if (commentsSection.length === 0 || !$('body').hasClass('single-post')) return;

        const postId = $('article.post').attr('id');
        if (!postId) return;

        const postIdNumber = postId.replace('post-', '');
        
        // Check for new comments every 30 seconds
        setInterval(function() {
            $.ajax({
                url: pulsepress_engagement.ajax_url,
                type: 'POST',
                data: {
                    action: 'check_new_comments',
                    post_id: postIdNumber,
                    nonce: pulsepress_engagement.nonce
                },
                success: function(response) {
                    if (response.success && response.data.has_new) {
                        // Show notification for new comments
                        showNewCommentsNotification(response.data.count);
                    }
                }
            });
        }, 30000); // 30 seconds
    }

    /**
     * Show new comments notification
     */
    function showNewCommentsNotification(count) {
        const notification = $('<div class="new-comments-notification">')
            .text(count + ' new comment' + (count > 1 ? 's' : ''))
            .append('<button class="refresh-comments">Refresh</button>');
        
        $('body').append(notification);
        
        notification.fadeIn(300);
        
        notification.find('.refresh-comments').on('click', function() {
            location.reload();
        });
        
        // Auto-hide after 10 seconds
        setTimeout(function() {
            notification.fadeOut(300, function() {
                $(this).remove();
            });
        }, 10000);
    }

    // Initialize additional features
    $(document).ready(function() {
        initSmoothScrollToComments();
        initAutoRefresh();
        initLiveComments();
    });

})(jQuery);
