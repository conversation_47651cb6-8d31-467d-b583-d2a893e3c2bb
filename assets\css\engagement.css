/**
 * PulsePress Engagement Features Styles
 */

/* ===============================
   Reading Progress Bar
=============================== */
.reading-progress-bar {
    position: fixed;
    top: 0;
    left: 0;
    width: 100%;
    height: 4px;
    background: rgba(0, 0, 0, 0.1);
    z-index: 9999;
    opacity: 0;
    transition: opacity 0.3s ease;
}

.reading-progress-bar.visible {
    opacity: 1;
}

.reading-progress-fill {
    height: 100%;
    background: linear-gradient(90deg, var(--primary-color), var(--secondary-color));
    width: 0%;
    transition: width 0.1s ease;
}

/* Dark mode */
[data-theme="dark"] .reading-progress-bar {
    background: rgba(255, 255, 255, 0.1);
}

/* ===============================
   Reading Time
=============================== */
.reading-time {
    display: inline-flex;
    align-items: center;
    gap: 0.5rem;
    font-size: 0.875rem;
    color: var(--gray-color);
    margin-bottom: 1rem;
}

.reading-time i {
    font-size: 0.75rem;
}

.reading-progress-text {
    font-weight: 500;
    color: var(--primary-color);
}

/* ===============================
   Post Reactions
=============================== */
.post-reactions {
    margin: 2rem 0;
    padding: 1.5rem;
    background: var(--light-color);
    border-radius: var(--border-radius);
    border: 1px solid #e9ecef;
}

.post-reactions h4 {
    margin: 0 0 1rem 0;
    font-size: 1.125rem;
    color: var(--dark-color);
}

.reaction-buttons {
    display: flex;
    flex-wrap: wrap;
    gap: 0.5rem;
}

.reaction-btn {
    display: flex;
    flex-direction: column;
    align-items: center;
    gap: 0.25rem;
    padding: 0.75rem 0.5rem;
    background: white;
    border: 2px solid #e9ecef;
    border-radius: var(--border-radius);
    cursor: pointer;
    transition: all 0.3s ease;
    min-width: 60px;
    position: relative;
    overflow: hidden;
}

.reaction-btn:hover {
    border-color: var(--primary-color);
    transform: translateY(-2px);
    box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);
}

.reaction-btn.active {
    border-color: var(--primary-color);
    background: var(--primary-color);
    color: white;
}

.reaction-btn.loading {
    opacity: 0.6;
    pointer-events: none;
}

.reaction-btn.reacted {
    animation: reactionPulse 0.3s ease;
}

@keyframes reactionPulse {
    0% { transform: scale(1); }
    50% { transform: scale(1.1); }
    100% { transform: scale(1); }
}

.reaction-icon {
    font-size: 1.25rem;
    line-height: 1;
}

.reaction-label {
    font-size: 0.75rem;
    font-weight: 500;
    text-transform: uppercase;
    letter-spacing: 0.5px;
}

.reaction-count {
    font-size: 0.875rem;
    font-weight: 600;
    background: rgba(0, 0, 0, 0.1);
    padding: 0.125rem 0.375rem;
    border-radius: 10px;
    min-width: 20px;
    text-align: center;
}

.reaction-btn.active .reaction-count {
    background: rgba(255, 255, 255, 0.2);
}

/* Dark mode */
[data-theme="dark"] .post-reactions {
    background: var(--card-bg);
    border-color: #333;
}

[data-theme="dark"] .post-reactions h4 {
    color: var(--text-color);
}

[data-theme="dark"] .reaction-btn {
    background: var(--dark-color);
    border-color: #333;
    color: var(--text-color);
}

[data-theme="dark"] .reaction-btn:hover {
    border-color: var(--primary-color);
}

/* ===============================
   Infinite Scroll
=============================== */
.infinite-scroll-container {
    margin: 2rem 0;
}

.posts-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
    gap: 2rem;
    margin-bottom: 2rem;
}

.load-more-container {
    text-align: center;
    margin: 2rem 0;
}

.load-more-btn {
    background: var(--primary-color);
    color: white;
    border: none;
    padding: 0.875rem 2rem;
    border-radius: var(--border-radius);
    font-weight: 500;
    cursor: pointer;
    transition: all 0.3s ease;
    font-size: 1rem;
}

.load-more-btn:hover {
    background: var(--secondary-color);
    transform: translateY(-2px);
    box-shadow: 0 4px 12px rgba(0, 0, 0, 0.2);
}

.load-more-btn:disabled {
    opacity: 0.6;
    cursor: not-allowed;
    transform: none;
}

.loading-spinner {
    margin: 1rem 0;
}

.loading-spinner i {
    font-size: 1.5rem;
    color: var(--primary-color);
}

/* ===============================
   New Comments Notification
=============================== */
.new-comments-notification {
    position: fixed;
    bottom: 20px;
    right: 20px;
    background: var(--primary-color);
    color: white;
    padding: 1rem 1.5rem;
    border-radius: var(--border-radius);
    box-shadow: var(--box-shadow);
    z-index: 1000;
    display: none;
    max-width: 300px;
}

.refresh-comments {
    background: rgba(255, 255, 255, 0.2);
    color: white;
    border: none;
    padding: 0.5rem 1rem;
    border-radius: var(--border-radius);
    margin-left: 1rem;
    cursor: pointer;
    transition: background 0.3s ease;
}

.refresh-comments:hover {
    background: rgba(255, 255, 255, 0.3);
}

/* ===============================
   Responsive Design
=============================== */
@media (max-width: 768px) {
    .reaction-buttons {
        justify-content: center;
    }
    
    .reaction-btn {
        min-width: 50px;
        padding: 0.5rem 0.25rem;
    }
    
    .reaction-icon {
        font-size: 1rem;
    }
    
    .reaction-label {
        font-size: 0.625rem;
    }
    
    .posts-grid {
        grid-template-columns: 1fr;
        gap: 1rem;
    }
    
    .post-reactions {
        padding: 1rem;
    }
    
    .new-comments-notification {
        bottom: 10px;
        right: 10px;
        left: 10px;
        max-width: none;
    }
}

@media (max-width: 480px) {
    .reaction-buttons {
        gap: 0.25rem;
    }
    
    .reaction-btn {
        min-width: 45px;
        padding: 0.375rem 0.125rem;
    }
    
    .reaction-label {
        display: none;
    }
    
    .load-more-btn {
        padding: 0.75rem 1.5rem;
        font-size: 0.875rem;
    }
}

/* ===============================
   Accessibility
=============================== */
.reaction-btn:focus {
    outline: 2px solid var(--primary-color);
    outline-offset: 2px;
}

.load-more-btn:focus {
    outline: 2px solid var(--primary-color);
    outline-offset: 2px;
}

/* Reduced motion */
@media (prefers-reduced-motion: reduce) {
    .reading-progress-fill,
    .reaction-btn,
    .load-more-btn {
        transition: none;
    }
    
    .reaction-btn.reacted {
        animation: none;
    }
}

/* High contrast mode */
@media (prefers-contrast: high) {
    .reaction-btn {
        border-width: 3px;
    }
    
    .reading-progress-bar {
        height: 6px;
    }
}

/* ===============================
   Print Styles
=============================== */
@media print {
    .reading-progress-bar,
    .post-reactions,
    .load-more-container,
    .new-comments-notification {
        display: none !important;
    }
}
