<?php
/**
 * The header for our theme
 *
 * This is the template that displays all of the <head> section and everything up until <div id="content">
 *
 * @link https://developer.wordpress.org/themes/basics/template-files/#template-partials
 *
 * @package PulsePress
 */

?>
<!doctype html>
<html <?php language_attributes(); ?>>
<head>
	<meta charset="<?php bloginfo( 'charset' ); ?>">
	<meta name="viewport" content="width=device-width, initial-scale=1">
	<link rel="profile" href="https://gmpg.org/xfn/11">

	<?php wp_head(); ?>
</head>

<body <?php body_class(); ?>>
<?php wp_body_open(); ?>
<div id="page" class="site">
	<a class="skip-link screen-reader-text" href="#primary"><?php esc_html_e( 'Skip to content', 'pulsepress' ); ?></a>

	<header id="masthead" class="site-header">
		<div class="header-top">
			<div class="container">
				<div class="trending-ticker">
					<span class="trending-ticker-label"><?php esc_html_e( 'Trending', 'pulsepress' ); ?></span>
					<div class="trending-ticker-content">
						<?php
						// Get trending posts (most viewed or commented in the last week)
						$args = array(
							'post_type'      => 'post',
							'posts_per_page' => 5,
							'meta_key'       => 'post_views_count', // Assuming you have a post views counter
							'orderby'        => 'meta_value_num',
							'order'          => 'DESC',
							'date_query'     => array(
								array(
									'after' => '1 week ago',
								),
							),
						);
						
						$trending_query = new WP_Query( $args );
						
						if ( $trending_query->have_posts() ) :
							while ( $trending_query->have_posts() ) :
								$trending_query->the_post();
								?>
								<span class="trending-ticker-item">
									<a href="<?php the_permalink(); ?>"><?php the_title(); ?></a>
								</span>
								<?php
							endwhile;
							wp_reset_postdata();
						else :
							?>
							<span class="trending-ticker-item">
								<a href="#"><?php esc_html_e( 'Welcome to PulsePress News Theme', 'pulsepress' ); ?></a>
							</span>
							<span class="trending-ticker-item">
								<a href="#"><?php esc_html_e( 'Breaking News: Latest Updates', 'pulsepress' ); ?></a>
							</span>
							<span class="trending-ticker-item">
								<a href="#"><?php esc_html_e( 'Technology: New Innovations', 'pulsepress' ); ?></a>
							</span>
							<span class="trending-ticker-item">
								<a href="#"><?php esc_html_e( 'Sports: Latest Results', 'pulsepress' ); ?></a>
							</span>
							<span class="trending-ticker-item">
								<a href="#"><?php esc_html_e( 'Business: Market Updates', 'pulsepress' ); ?></a>
							</span>
							<?php
						endif;
						?>
					</div>
				</div>
			</div>
		</div>

		<div class="header-main">
			<div class="container">
				<div class="site-branding">
					<?php
					the_custom_logo();
					if ( is_front_page() && is_home() ) :
						?>
						<h1 class="site-title"><a href="<?php echo esc_url( home_url( '/' ) ); ?>" rel="home"><?php bloginfo( 'name' ); ?></a></h1>
						<?php
					else :
						?>
						<p class="site-title"><a href="<?php echo esc_url( home_url( '/' ) ); ?>" rel="home"><?php bloginfo( 'name' ); ?></a></p>
						<?php
					endif;
					$pulsepress_description = get_bloginfo( 'description', 'display' );
					if ( $pulsepress_description || is_customize_preview() ) :
						?>
						<p class="site-description"><?php echo $pulsepress_description; // phpcs:ignore WordPress.Security.EscapeOutput.OutputNotEscaped ?></p>
					<?php endif; ?>
				</div><!-- .site-branding -->

				<div class="header-search">
					<?php get_search_form(); ?>
				</div>

				<div class="dark-mode-toggle" aria-label="<?php esc_attr_e( 'Toggle Dark Mode', 'pulsepress' ); ?>" role="button" tabindex="0">
					<span class="screen-reader-text"><?php esc_html_e( 'Toggle Dark Mode', 'pulsepress' ); ?></span>
					<i class="fas fa-moon"></i>
				</div>
			</div>
		</div>

		<nav id="site-navigation" class="main-navigation">
			<div class="container">
				<button class="menu-toggle" aria-controls="primary-menu" aria-expanded="false">
					<i class="fas fa-bars"></i>
					<span class="screen-reader-text"><?php esc_html_e( 'Menu', 'pulsepress' ); ?></span>
				</button>
				<?php
				wp_nav_menu(
					array(
						'theme_location' => 'primary',
						'menu_id'        => 'primary-menu',
						'menu_class'     => 'main-menu',
						'container'      => false,
						'fallback_cb'    => 'pulsepress_primary_menu_fallback',
					)
				);
				
				// Fallback if no menu is assigned
				function pulsepress_primary_menu_fallback() {
					echo '<ul class="main-menu">';
					echo '<li class="current-menu-item"><a href="' . esc_url( home_url( '/' ) ) . '">' . esc_html__( 'Home', 'pulsepress' ) . '</a></li>';
					echo '<li class="has-mega-menu"><a href="#">' . esc_html__( 'News', 'pulsepress' ) . '</a>';
					echo '<div class="mega-menu">';
					echo '<div class="mega-menu-column">';
					echo '<h4 class="mega-menu-title">' . esc_html__( 'Technology', 'pulsepress' ) . '</h4>';
					echo '<ul class="mega-menu-list">';
					echo '<li><a href="#">' . esc_html__( 'Gadgets', 'pulsepress' ) . '</a></li>';
					echo '<li><a href="#">' . esc_html__( 'Software', 'pulsepress' ) . '</a></li>';
					echo '<li><a href="#">' . esc_html__( 'AI', 'pulsepress' ) . '</a></li>';
					echo '</ul>';
					echo '</div>';
					echo '<div class="mega-menu-column">';
					echo '<h4 class="mega-menu-title">' . esc_html__( 'Business', 'pulsepress' ) . '</h4>';
					echo '<ul class="mega-menu-list">';
					echo '<li><a href="#">' . esc_html__( 'Markets', 'pulsepress' ) . '</a></li>';
					echo '<li><a href="#">' . esc_html__( 'Economy', 'pulsepress' ) . '</a></li>';
					echo '<li><a href="#">' . esc_html__( 'Startups', 'pulsepress' ) . '</a></li>';
					echo '</ul>';
					echo '</div>';
					echo '<div class="mega-menu-column">';
					echo '<h4 class="mega-menu-title">' . esc_html__( 'Sports', 'pulsepress' ) . '</h4>';
					echo '<ul class="mega-menu-list">';
					echo '<li><a href="#">' . esc_html__( 'Football', 'pulsepress' ) . '</a></li>';
					echo '<li><a href="#">' . esc_html__( 'Basketball', 'pulsepress' ) . '</a></li>';
					echo '<li><a href="#">' . esc_html__( 'Tennis', 'pulsepress' ) . '</a></li>';
					echo '</ul>';
					echo '</div>';
					echo '</div>';
					echo '</li>';
					echo '<li><a href="#">' . esc_html__( 'Technology', 'pulsepress' ) . '</a></li>';
					echo '<li><a href="#">' . esc_html__( 'Business', 'pulsepress' ) . '</a></li>';
					echo '<li><a href="#">' . esc_html__( 'Sports', 'pulsepress' ) . '</a></li>';
					echo '<li><a href="#">' . esc_html__( 'Entertainment', 'pulsepress' ) . '</a></li>';
					echo '<li><a href="#">' . esc_html__( 'Health', 'pulsepress' ) . '</a></li>';
					echo '<li><a href="#">' . esc_html__( 'Contact', 'pulsepress' ) . '</a></li>';
					echo '</ul>';
				}
				?>
			</div>
		</nav><!-- #site-navigation -->
	</header><!-- #masthead -->
