<?php
/**
 * The main template file
 *
 * This is the most generic template file in a WordPress theme
 * and one of the two required files for a theme (the other being style.css).
 * It is used to display a page when nothing more specific matches a query.
 * E.g., it puts together the home page when no home.php file exists.
 *
 * @link https://developer.wordpress.org/themes/basics/template-hierarchy/
 *
 * @package PulsePress
 */

get_header();
?>

	<main id="primary" class="site-main">
		<div class="container">
			<div class="row">
				<div class="col-lg-8">
					<?php
					if ( have_posts() ) :

						if ( is_home() && ! is_front_page() ) :
							?>
							<header class="page-header">
								<h1 class="page-title"><?php single_post_title(); ?></h1>
							</header>
							<?php
						endif;

						/* Start the Loop */
						while ( have_posts() ) :
							the_post();

							/*
							 * Include the Post-Type-specific template for the content.
							 * If you want to override this in a child theme, then include a file
							 * called content-___.php (where ___ is the Post Type name) and that will be used instead.
							 */
							get_template_part( 'template-parts/content/content', get_post_type() );

						endwhile;

						the_posts_pagination( array(
							'prev_text' => '<i class="fas fa-arrow-left"></i> ' . esc_html__( 'Previous', 'pulsepress' ),
							'next_text' => esc_html__( 'Next', 'pulsepress' ) . ' <i class="fas fa-arrow-right"></i>',
							'mid_size'  => 2,
						) );

					else :

						get_template_part( 'template-parts/content/content', 'none' );

					endif;
					?>
				</div>
				
				<div class="col-lg-4">
					<?php get_sidebar(); ?>
				</div>
			</div>
		</div>
	</main><!-- #main -->

<?php
get_footer();
