<?php
/**
 * PulsePress functions and definitions
 *
 * @link https://developer.wordpress.org/themes/basics/theme-functions/
 *
 * @package PulsePress
 */

if ( ! defined( 'PULSEPRESS_VERSION' ) ) {
	// Replace the version number of the theme on each release.
	define( 'PULSEPRESS_VERSION', '1.0.0' );
}

/**
 * Sets up theme defaults and registers support for various WordPress features.
 */
function pulsepress_setup() {
	/*
	 * Make theme available for translation.
	 * Translations can be filed in the /languages/ directory.
	 */
	load_theme_textdomain( 'pulsepress', get_template_directory() . '/languages' );

	// Add default posts and comments RSS feed links to head.
	add_theme_support( 'automatic-feed-links' );

	/*
	 * Let WordPress manage the document title.
	 * By adding theme support, we declare that this theme does not use a
	 * hard-coded <title> tag in the document head, and expect WordPress to
	 * provide it for us.
	 */
	add_theme_support( 'title-tag' );

	/*
	 * Enable support for Post Thumbnails on posts and pages.
	 *
	 * @link https://developer.wordpress.org/themes/functionality/featured-images-post-thumbnails/
	 */
	add_theme_support( 'post-thumbnails' );

	// Define custom image sizes
	add_image_size( 'pulsepress-featured', 1200, 628, true );
	add_image_size( 'pulsepress-grid', 600, 400, true );
	add_image_size( 'pulsepress-thumbnail', 150, 150, true );

	// This theme uses wp_nav_menu() in multiple locations.
	register_nav_menus(
		array(
			'primary' => esc_html__( 'Primary Menu', 'pulsepress' ),
			'footer' => esc_html__( 'Footer Menu', 'pulsepress' ),
			'social' => esc_html__( 'Social Links Menu', 'pulsepress' ),
		)
	);

	/*
	 * Switch default core markup for search form, comment form, and comments
	 * to output valid HTML5.
	 */
	add_theme_support(
		'html5',
		array(
			'search-form',
			'comment-form',
			'comment-list',
			'gallery',
			'caption',
			'style',
			'script',
		)
	);

	// Set up the WordPress core custom background feature.
	add_theme_support(
		'custom-background',
		apply_filters(
			'pulsepress_custom_background_args',
			array(
				'default-color' => 'ffffff',
				'default-image' => '',
			)
		)
	);

	// Add theme support for selective refresh for widgets.
	add_theme_support( 'customize-selective-refresh-widgets' );

	/**
	 * Add support for core custom logo.
	 *
	 * @link https://codex.wordpress.org/Theme_Logo
	 */
	add_theme_support(
		'custom-logo',
		array(
			'height'      => 250,
			'width'       => 250,
			'flex-width'  => true,
			'flex-height' => true,
		)
	);

	// Add support for full and wide align images.
	add_theme_support( 'align-wide' );

	// Add support for responsive embeds.
	add_theme_support( 'responsive-embeds' );

	// Add support for custom color scheme.
	add_theme_support( 'editor-color-palette', array(
		array(
			'name'  => esc_html__( 'Primary', 'pulsepress' ),
			'slug'  => 'primary',
			'color' => '#1e73be',
		),
		array(
			'name'  => esc_html__( 'Secondary', 'pulsepress' ),
			'slug'  => 'secondary',
			'color' => '#ff6b6b',
		),
		array(
			'name'  => esc_html__( 'Dark', 'pulsepress' ),
			'slug'  => 'dark',
			'color' => '#2c3e50',
		),
		array(
			'name'  => esc_html__( 'Light', 'pulsepress' ),
			'slug'  => 'light',
			'color' => '#f8f9fa',
		),
	) );

	// Add support for editor styles.
	add_theme_support( 'editor-styles' );

	// Enqueue editor styles.
	add_editor_style( 'assets/css/editor-style.css' );
}
add_action( 'after_setup_theme', 'pulsepress_setup' );

/**
 * Set the content width in pixels, based on the theme's design and stylesheet.
 *
 * Priority 0 to make it available to lower priority callbacks.
 *
 * @global int $content_width
 */
function pulsepress_content_width() {
	$GLOBALS['content_width'] = apply_filters( 'pulsepress_content_width', 1140 );
}
add_action( 'after_setup_theme', 'pulsepress_content_width', 0 );

/**
 * Register widget area.
 *
 * @link https://developer.wordpress.org/themes/functionality/sidebars/#registering-a-sidebar
 */
function pulsepress_widgets_init() {
	register_sidebar(
		array(
			'name'          => esc_html__( 'Sidebar', 'pulsepress' ),
			'id'            => 'sidebar-1',
			'description'   => esc_html__( 'Add widgets here.', 'pulsepress' ),
			'before_widget' => '<section id="%1$s" class="widget %2$s">',
			'after_widget'  => '</section>',
			'before_title'  => '<h2 class="widget-title">',
			'after_title'   => '</h2>',
		)
	);

	register_sidebar(
		array(
			'name'          => esc_html__( 'Footer 1', 'pulsepress' ),
			'id'            => 'footer-1',
			'description'   => esc_html__( 'Add footer widgets here.', 'pulsepress' ),
			'before_widget' => '<section id="%1$s" class="widget %2$s">',
			'after_widget'  => '</section>',
			'before_title'  => '<h2 class="widget-title">',
			'after_title'   => '</h2>',
		)
	);

	register_sidebar(
		array(
			'name'          => esc_html__( 'Footer 2', 'pulsepress' ),
			'id'            => 'footer-2',
			'description'   => esc_html__( 'Add footer widgets here.', 'pulsepress' ),
			'before_widget' => '<section id="%1$s" class="widget %2$s">',
			'after_widget'  => '</section>',
			'before_title'  => '<h2 class="widget-title">',
			'after_title'   => '</h2>',
		)
	);

	register_sidebar(
		array(
			'name'          => esc_html__( 'Footer 3', 'pulsepress' ),
			'id'            => 'footer-3',
			'description'   => esc_html__( 'Add footer widgets here.', 'pulsepress' ),
			'before_widget' => '<section id="%1$s" class="widget %2$s">',
			'after_widget'  => '</section>',
			'before_title'  => '<h2 class="widget-title">',
			'after_title'   => '</h2>',
		)
	);

	register_sidebar(
		array(
			'name'          => esc_html__( 'Footer 4', 'pulsepress' ),
			'id'            => 'footer-4',
			'description'   => esc_html__( 'Add footer widgets here.', 'pulsepress' ),
			'before_widget' => '<section id="%1$s" class="widget %2$s">',
			'after_widget'  => '</section>',
			'before_title'  => '<h2 class="widget-title">',
			'after_title'   => '</h2>',
		)
	);
}
add_action( 'widgets_init', 'pulsepress_widgets_init' );

/**
 * Enqueue scripts and styles.
 */
function pulsepress_scripts() {
	// Enqueue Google Fonts with optimizations
	wp_enqueue_style( 'pulsepress-fonts', 'https://fonts.googleapis.com/css2?family=Montserrat:wght@400;500;700&family=Roboto:wght@400;500;700&display=swap', array(), null );

	// Main stylesheet with version for cache busting
	wp_enqueue_style( 'pulsepress-style', get_stylesheet_uri(), array(), PULSEPRESS_VERSION );

	// Main JavaScript with dependencies
	wp_enqueue_script( 'pulsepress-main', get_template_directory_uri() . '/assets/js/main.js', array(), PULSEPRESS_VERSION, true );

	// Font Awesome for icons - only load where needed
	if ( is_front_page() || is_single() || is_page() ) {
		wp_enqueue_script( 'font-awesome', 'https://kit.fontawesome.com/a076d05399.js', array(), null, true );
	}

	// Comment reply script only when needed
	if ( is_singular() && comments_open() && get_option( 'thread_comments' ) ) {
		wp_enqueue_script( 'comment-reply' );
	}

	// Localize script for AJAX
	wp_localize_script( 'pulsepress-main', 'pulsepress_ajax', array(
		'ajax_url' => admin_url( 'admin-ajax.php' ),
		'nonce'    => wp_create_nonce( 'pulsepress_nonce' ),
	) );
}
add_action( 'wp_enqueue_scripts', 'pulsepress_scripts' );

/**
 * Sanitize checkbox function for customizer
 */
if ( ! function_exists( 'pulsepress_sanitize_checkbox' ) ) {
	function pulsepress_sanitize_checkbox( $checked ) {
		return ( ( isset( $checked ) && true === $checked ) ? true : false );
	}
}

/**
 * Custom template tags for this theme.
 */
require get_template_directory() . '/inc/template-tags.php';

/**
 * Functions which enhance the theme by hooking into WordPress.
 */
require get_template_directory() . '/inc/template-functions.php';

/**
 * Customizer additions.
 */
require get_template_directory() . '/inc/customizer/customizer.php';

/**
 * Custom widgets.
 */
require get_template_directory() . '/inc/widgets/widgets.php';

/**
 * Social media integration.
 */
require get_template_directory() . '/inc/social-media/social-media.php';

/**
 * SEO enhancements.
 */
require get_template_directory() . '/inc/seo/seo.php';

/**
 * Performance optimizations.
 */
require get_template_directory() . '/inc/performance/performance.php';

// Temporarily disable these to test
/*
require get_template_directory() . '/inc/ai-features/ai-features.php';
require get_template_directory() . '/inc/engagement/engagement.php';
require get_template_directory() . '/inc/pwa/pwa.php';
require get_template_directory() . '/inc/blocks/blocks.php';
require get_template_directory() . '/inc/accessibility/accessibility.php';
*/

/**
 * Load Jetpack compatibility file.
 */
if ( defined( 'JETPACK__VERSION' ) ) {
	require get_template_directory() . '/inc/jetpack.php';
}

// Temporarily disable additional performance optimizations to test
/*
remove_action( 'wp_head', 'wp_generator' );
remove_action( 'wp_head', 'wlwmanifest_link' );
remove_action( 'wp_head', 'rsd_link' );
remove_action( 'wp_head', 'wp_shortlink_wp_head', 10, 0 );

add_filter( 'xmlrpc_enabled', '__return_false' );

function pulsepress_remove_version_scripts_styles( $src ) {
	if ( strpos( $src, 'ver=' ) ) {
		$src = remove_query_arg( 'ver', $src );
	}
	return $src;
}
add_filter( 'style_loader_src', 'pulsepress_remove_version_scripts_styles', 9999 );
add_filter( 'script_loader_src', 'pulsepress_remove_version_scripts_styles', 9999 );

function pulsepress_optimize_heartbeat( $settings ) {
	$settings['interval'] = 60;
	return $settings;
}
add_filter( 'heartbeat_settings', 'pulsepress_optimize_heartbeat' );

function pulsepress_disable_heartbeat_frontend() {
	if ( ! is_admin() ) {
		wp_deregister_script( 'heartbeat' );
	}
}
add_action( 'init', 'pulsepress_disable_heartbeat_frontend', 1 );

function pulsepress_optimize_database() {
	remove_action( 'wp_head', 'adjacent_posts_rel_link_wp_head', 10, 0 );
	remove_action( 'wp_head', 'wp_generator' );
	remove_action( 'wp_head', 'wp_shortlink_wp_head', 10, 0 );
}
add_action( 'init', 'pulsepress_optimize_database' );

function pulsepress_add_expires_headers() {
	if ( ! is_admin() ) {
		$expires = 60 * 60 * 24 * 30;
		header( 'Cache-Control: public, max-age=' . $expires );
		header( 'Expires: ' . gmdate( 'D, d M Y H:i:s', time() + $expires ) . ' GMT' );
	}
}
add_action( 'send_headers', 'pulsepress_add_expires_headers' );
*/
