# PulsePress WordPress Theme

PulsePress is a fully responsive, SEO-optimized, and social-media-integrated WordPress news theme designed for news, magazine, and blogging websites. Enhanced with AI-powered features, advanced performance optimizations, and comprehensive accessibility support.

## 🚀 Enhanced Features

### 📈 Performance & Speed Optimization
- **Native Lazy Loading**: Automatic image lazy loading with fallback support
- **Optimized CSS/JS Delivery**: Conditional loading and async/defer attributes
- **Critical CSS Inlining**: Above-the-fold content optimization
- **Resource Preloading**: DNS prefetch and preload hints for critical resources
- **Gzip Compression**: Automatic compression for faster loading
- **Query Optimization**: Reduced database queries and optimized meta queries

### 🤖 AI-Powered Features
- **Auto-Generated Summaries**: AI-powered content summarization with fallback extractive method
- **Smart Post Tagging**: Automatic tag generation using AI or keyword extraction
- **Reading Time Calculation**: Automatic estimation of article reading time
- **Content Analysis**: Built-in tools for content optimization

### 💡 User Engagement Enhancements
- **Infinite Scroll**: AJAX-powered infinite scrolling with manual and auto-load options
- **Reading Progress Bar**: Visual progress indicator for articles
- **Post Reaction Buttons**: Like, Love, Laugh, Wow, Sad, Angry reactions
- **Estimated Read Time**: Dynamic reading progress with completion tracking
- **Live Comment Updates**: Real-time comment notifications
- **Auto-Refresh Content**: Trending ticker auto-updates every 5 minutes

### 🌓 Enhanced Dark Mode
- **OS Preference Detection**: Automatic theme detection based on system preferences
- **Persistent Settings**: User preference storage with localStorage
- **Smooth Transitions**: Enhanced animations and transitions
- **Improved Accessibility**: Better contrast ratios and focus indicators

### 🔎 Advanced SEO Features
- **JSON-LD Structured Data**: Article, Organization, Website, and Breadcrumb schemas
- **Enhanced Open Graph Tags**: Complete social media optimization
- **Twitter Cards**: Rich Twitter card support with author attribution
- **Breadcrumbs with Schema**: SEO-friendly navigation with structured markup
- **Custom Meta Descriptions**: Built-in SEO meta box with character counting
- **Canonical URLs**: Automatic canonical URL generation

### 🧩 Custom Gutenberg Blocks
- **Editor's Picks Block**: Showcase featured articles with customizable layout
- **Weekly Top Stories**: Display trending content based on views and timeframe
- **Featured Category Block**: Highlight specific category content
- **Breaking News Block**: Urgent news announcements with animation effects

### 🔄 Auto Content Refresh & Live Updates
- **Trending Ticker Refresh**: Automatic content updates every 5 minutes
- **Live Comment Notifications**: Real-time comment count updates
- **Breaking News Alerts**: Instant notifications for urgent content
- **Background Sync**: Offline form submission support

### ♿ Comprehensive Accessibility
- **WCAG 2.1 AA Compliance**: Full accessibility standard compliance
- **Accessibility Toolbar**: Font size, contrast, and dyslexia-friendly options
- **Enhanced Focus Indicators**: Clear keyboard navigation support
- **Screen Reader Optimization**: ARIA labels and live regions
- **Skip Links**: Quick navigation for keyboard users
- **Color Contrast Validation**: Automatic contrast ratio checking

### 📱 Progressive Web App (PWA)
- **Installable App**: Full PWA support with manifest and service worker
- **Offline Functionality**: Cached content for offline reading
- **Push Notifications**: Breaking news and update notifications
- **App-like Experience**: Native app feel with splash screen and icons

### 🛠️ Developer Enhancements
- **Custom Hooks & Filters**: Extensive customization options for developers
- **Theme.json Support**: Full Gutenberg theme support with design tokens
- **Performance Monitoring**: Built-in performance statistics for admins
- **Modular Architecture**: Clean, organized code structure

## Original Features

- Mobile-first responsive layout (desktop, tablet, mobile)
- Sticky header with logo, menu, and search
- Trending news ticker on top
- Mega menu support for categories
- Custom homepage layout with featured posts slider and category sections
- Dark mode toggle
- Social media auto-posting integration
- SEO optimized with proper schema markup
- Custom widgets for popular posts, recent posts, social media, and newsletter
- Customizer options for colors, typography, and layout

## Installation

1. Upload the `pulsepress` folder to the `/wp-content/themes/` directory
2. Activate the theme through the 'Themes' menu in WordPress
3. Configure theme options via the Customizer

## 🔧 Configuration

### Performance Settings
Navigate to **Customizer > Performance Settings** to configure:
- Enable/disable WordPress emojis
- Control Gutenberg block CSS loading
- Add critical CSS for above-the-fold content
- Monitor performance statistics

### AI Features Setup
Navigate to **Customizer > AI Features** to configure:
- Add your AI API key (OpenAI, Claude, etc.)
- Enable auto-generated summaries
- Enable auto-tagging for posts
- Configure AI-powered content analysis

### User Engagement
Navigate to **Customizer > User Engagement** to configure:
- Enable reading progress bar
- Show estimated reading time
- Enable post reaction buttons
- Configure infinite scroll behavior

### SEO Settings
Navigate to **Customizer > SEO Settings** to configure:
- Enable/disable breadcrumbs
- Set Twitter handle for cards
- Configure structured data output

### PWA Configuration
Navigate to **Customizer > PWA Settings** to configure:
- Enable Progressive Web App features
- Set app name and short name
- Upload app icons (192x192 and 512x512)
- Configure app colors and theme

### Accessibility Options
Navigate to **Customizer > Accessibility** to configure:
- Show accessibility toolbar
- Enable skip links
- Configure focus indicators
- Set accessibility preferences

## 📝 Usage

### Using Custom Blocks
The theme includes several custom Gutenberg blocks:

#### Editor's Picks Block
```
/pulsepress/editors-picks
```
- Displays posts marked as editor's picks
- Customizable number of posts
- Toggle excerpt, date, and author display

#### Weekly Top Stories Block
```
/pulsepress/weekly-top-stories
```
- Shows most viewed posts by timeframe
- Configurable time period (day/week/month)
- Automatic view counting

#### Featured Category Block
```
/pulsepress/featured-category
```
- Highlight posts from specific categories
- Grid or list layout options
- Customizable post count

#### Breaking News Block
```
/pulsepress/breaking-news
```
- Urgent news announcements
- Animated display with optional urgency mode
- Customizable title and content

### AI Features Usage

#### Auto-Generated Summaries
- Enable in Customizer > AI Features
- Summaries are generated automatically when posts are saved
- Manual generation available in post editor sidebar
- Fallback extractive summarization if AI API unavailable

#### Smart Tagging
- Configure in post editor AI Features meta box
- Automatic tag suggestions based on content
- Option to auto-apply generated tags

### Performance Optimization

#### Critical CSS
Add critical CSS in Customizer > Performance Settings for above-the-fold content optimization.

#### Lazy Loading
Images automatically use native lazy loading with intersection observer fallback.

#### Caching
Service worker provides automatic caching for PWA functionality.

## 🎨 Customization

### Theme Colors
All colors are CSS custom properties and can be customized via:
- Customizer > Colors
- Direct CSS modification
- theme.json configuration

### Typography
Font families and sizes can be customized through:
- Customizer > Typography
- theme.json font settings
- CSS custom properties

### Layout
Container widths and spacing can be adjusted via:
- CSS custom properties
- theme.json layout settings
- Customizer options

## 🔌 Recommended Plugins

### Performance
- **Autoptimize**: CSS/JS optimization
- **WP Super Cache**: Page caching
- **Smush**: Image optimization

### SEO (Optional)
- **Yoast SEO**: Advanced SEO features
- **Rank Math**: SEO optimization
- **Schema Pro**: Enhanced structured data

### Accessibility
- **WP Accessibility**: Additional accessibility features
- **One Click Accessibility**: Quick accessibility improvements

## 🛠️ Developer Information

### Hooks and Filters
The theme provides numerous hooks for customization:

```php
// Custom action hooks
do_action( 'pulsepress_before_header' );
do_action( 'pulsepress_after_header' );
do_action( 'pulsepress_before_content' );
do_action( 'pulsepress_after_content' );
do_action( 'pulsepress_before_footer' );
do_action( 'pulsepress_after_footer' );

// Custom filters
apply_filters( 'pulsepress_post_meta', $meta );
apply_filters( 'pulsepress_excerpt_length', 25 );
apply_filters( 'pulsepress_social_share_platforms', $platforms );
```

### File Structure
```
pulsepress/
├── assets/
│   ├── css/
│   │   ├── main.css
│   │   ├── engagement.css
│   │   └── blocks.css
│   ├── js/
│   │   ├── main.js
│   │   ├── engagement.js
│   │   └── accessibility.js
│   └── images/
├── inc/
│   ├── accessibility/
│   ├── ai-features/
│   ├── blocks/
│   ├── customizer/
│   ├── engagement/
│   ├── performance/
│   ├── pwa/
│   ├── seo/
│   └── social-media/
├── template-parts/
└── theme.json
```

## 📊 Performance Metrics

The theme is optimized for:
- **Core Web Vitals**: Excellent LCP, FID, and CLS scores
- **PageSpeed Insights**: 90+ scores on mobile and desktop
- **GTmetrix**: Grade A performance
- **Accessibility**: WCAG 2.1 AA compliance

## 🔄 Updates and Support

### Automatic Updates
The theme supports WordPress automatic updates when distributed through the repository.

### Manual Updates
1. Download the latest version
2. Replace theme files via FTP or WordPress admin
3. Clear any caching plugins

### Support
- GitHub Issues: Report bugs and feature requests
- Documentation: Comprehensive online documentation
- Community: WordPress.org support forums

## 📄 License

This theme is licensed under the GNU General Public License v2 or later.

## 🙏 Credits

- **Font Awesome**: Icons
- **Google Fonts**: Typography
- **Intersection Observer**: Lazy loading fallback
- **WordPress**: Core functionality

## Theme Setup

### Menus

PulsePress supports three menu locations:

1. **Primary Menu** - Main navigation menu
2. **Footer Menu** - Links in the footer
3. **Social Links Menu** - Social media links

To set up menus:

1. Go to Appearance > Menus
2. Create a new menu or select an existing one
3. Add menu items
4. Select the display location
5. Save the menu

### Homepage Setup

To set up the custom homepage:

1. Create a new page (e.g., "Home")
2. Go to Settings > Reading
3. Set "Your homepage displays" to "A static page"
4. Select your new page as the Homepage
5. The theme will automatically apply the custom homepage template

### Featured Posts

To display posts in the featured slider on the homepage:

1. Create a category called "Featured"
2. Add posts to this category
3. These posts will appear in the featured slider

### Social Media Integration

To enable automatic post sharing to social media:

1. Go to Appearance > Social Media
2. Enter your API credentials for each platform
3. Follow the instructions to obtain access tokens
4. Enable auto-sharing for each platform

## Customization

### Theme Customizer

PulsePress includes extensive customization options via the WordPress Customizer:

1. Go to Appearance > Customize
2. Explore the following sections:
   - **Theme Options** - General theme settings
   - **Color Scheme** - Customize theme colors
   - **Typography** - Change fonts and text sizes
   - **Layout** - Adjust layout options
   - **Social Media** - Set up social media links

### Widgets

PulsePress includes several custom widgets:

1. **PulsePress: Recent Posts** - Display recent posts with thumbnails
2. **PulsePress: Popular Posts** - Display popular posts by views or comments
3. **PulsePress: Social Media** - Display social media links
4. **PulsePress: Newsletter** - Display a newsletter signup form

To add widgets:

1. Go to Appearance > Widgets
2. Drag and drop widgets to the desired widget areas
3. Configure widget settings
4. Save changes

## Credits

- Font Awesome for icons
- Google Fonts for typography
- Developed by [Your Name]

## Support

For theme support, please contact [your email or support URL].

## License

This theme is licensed under the GNU General Public License v2 or later.
