<?php
/**
 * Functions which enhance the theme by hooking into WordPress
 *
 * @package PulsePress
 */

/**
 * Adds custom classes to the array of body classes.
 *
 * @param array $classes Classes for the body element.
 * @return array
 */
function pulsepress_body_classes( $classes ) {
	// Adds a class of hfeed to non-singular pages.
	if ( ! is_singular() ) {
		$classes[] = 'hfeed';
	}

	// Adds a class of no-sidebar when there is no sidebar present.
	if ( ! is_active_sidebar( 'sidebar-1' ) ) {
		$classes[] = 'no-sidebar';
	}

	// Add a class if dark mode is active.
	$dark_mode = get_theme_mod( 'pulsepress_dark_mode', false );
	if ( $dark_mode ) {
		$classes[] = 'dark-mode';
	}

	return $classes;
}
add_filter( 'body_class', 'pulsepress_body_classes' );

/**
 * Add a pingback url auto-discovery header for single posts, pages, or attachments.
 */
function pulsepress_pingback_header() {
	if ( is_singular() && pings_open() ) {
		printf( '<link rel="pingback" href="%s">', esc_url( get_bloginfo( 'pingback_url' ) ) );
	}
}
add_action( 'wp_head', 'pulsepress_pingback_header' );

/**
 * Add schema markup to the body element.
 *
 * @param array $attributes An array of body attributes.
 * @return array
 */
function pulsepress_body_attributes( $attributes ) {
	if ( is_singular( 'post' ) ) {
		$attributes['itemscope'] = '';
		$attributes['itemtype']  = 'https://schema.org/Article';
	} elseif ( is_page() ) {
		$attributes['itemscope'] = '';
		$attributes['itemtype']  = 'https://schema.org/WebPage';
	} elseif ( is_search() ) {
		$attributes['itemscope'] = '';
		$attributes['itemtype']  = 'https://schema.org/SearchResultsPage';
	} else {
		$attributes['itemscope'] = '';
		$attributes['itemtype']  = 'https://schema.org/WebSite';
	}

	return $attributes;
}
add_filter( 'wp_body_open_attributes', 'pulsepress_body_attributes' );

/**
 * Adds schema markup to the post elements.
 *
 * @param array $attributes An array of post attributes.
 * @return array
 */
function pulsepress_post_attributes( $attributes ) {
	if ( is_singular( 'post' ) ) {
		$attributes['itemscope'] = '';
		$attributes['itemtype']  = 'https://schema.org/Article';
	}

	return $attributes;
}
add_filter( 'post_class', 'pulsepress_post_attributes' );

/**
 * Modify the excerpt length.
 *
 * @param int $length Excerpt length.
 * @return int Modified excerpt length.
 */
function pulsepress_excerpt_length( $length ) {
	return 20;
}
add_filter( 'excerpt_length', 'pulsepress_excerpt_length' );

/**
 * Modify the excerpt more string.
 *
 * @param string $more The string shown within the more link.
 * @return string
 */
function pulsepress_excerpt_more( $more ) {
	return '...';
}
add_filter( 'excerpt_more', 'pulsepress_excerpt_more' );

/**
 * Add custom meta tags for SEO and social sharing.
 */
function pulsepress_meta_tags() {
	global $post;

	if ( is_singular() && $post ) {
		// Get post thumbnail
		$thumbnail_url = get_the_post_thumbnail_url( $post->ID, 'full' );
		
		// Get post excerpt
		$excerpt = wp_strip_all_tags( get_the_excerpt(), true );
		if ( ! $excerpt ) {
			$excerpt = wp_strip_all_tags( wp_trim_words( $post->post_content, 55, '' ), true );
		}
		
		// Open Graph meta tags
		echo '<meta property="og:title" content="' . esc_attr( get_the_title() ) . '" />' . "\n";
		echo '<meta property="og:type" content="article" />' . "\n";
		echo '<meta property="og:url" content="' . esc_url( get_permalink() ) . '" />' . "\n";
		if ( $thumbnail_url ) {
			echo '<meta property="og:image" content="' . esc_url( $thumbnail_url ) . '" />' . "\n";
		}
		echo '<meta property="og:description" content="' . esc_attr( $excerpt ) . '" />' . "\n";
		echo '<meta property="og:site_name" content="' . esc_attr( get_bloginfo( 'name' ) ) . '" />' . "\n";
		
		// Twitter Card meta tags
		echo '<meta name="twitter:card" content="summary_large_image" />' . "\n";
		echo '<meta name="twitter:title" content="' . esc_attr( get_the_title() ) . '" />' . "\n";
		echo '<meta name="twitter:description" content="' . esc_attr( $excerpt ) . '" />' . "\n";
		if ( $thumbnail_url ) {
			echo '<meta name="twitter:image" content="' . esc_url( $thumbnail_url ) . '" />' . "\n";
		}
	}
}
add_action( 'wp_head', 'pulsepress_meta_tags' );

/**
 * Track post views.
 */
function pulsepress_track_post_views() {
	if ( is_singular( 'post' ) ) {
		global $post;
		$post_id = $post->ID;
		
		// Get current view count
		$views = get_post_meta( $post_id, 'post_views_count', true );
		
		// Update view count
		if ( $views === '' ) {
			update_post_meta( $post_id, 'post_views_count', 1 );
		} else {
			$views = intval( $views );
			update_post_meta( $post_id, 'post_views_count', $views + 1 );
		}
	}
}
add_action( 'wp', 'pulsepress_track_post_views' );

/**
 * Add structured data for posts.
 */
function pulsepress_structured_data() {
	if ( is_singular( 'post' ) ) {
		global $post;
		
		// Get post data
		$title = get_the_title();
		$permalink = get_permalink();
		$excerpt = wp_strip_all_tags( get_the_excerpt(), true );
		$date_published = get_the_date( 'c' );
		$date_modified = get_the_modified_date( 'c' );
		$author_name = get_the_author();
		$author_id = get_the_author_meta( 'ID' );
		$author_url = $author_id ? get_author_posts_url( $author_id ) : '';
		$thumbnail_url = get_the_post_thumbnail_url( $post->ID, 'full' );
		
		// Build structured data
		$structured_data = array(
			'@context' => 'https://schema.org',
			'@type' => 'Article',
			'mainEntityOfPage' => array(
				'@type' => 'WebPage',
				'@id' => $permalink,
			),
			'headline' => $title,
			'description' => $excerpt,
			'datePublished' => $date_published,
			'dateModified' => $date_modified,
			'author' => array(
				'@type' => 'Person',
				'name' => $author_name,
				'url' => $author_url,
			),
			'publisher' => array(
				'@type' => 'Organization',
				'name' => get_bloginfo( 'name' ),
				'logo' => array(
					'@type' => 'ImageObject',
					'url' => get_custom_logo_url(),
				),
			),
		);
		
		if ( $thumbnail_url ) {
			$structured_data['image'] = array(
				'@type' => 'ImageObject',
				'url' => $thumbnail_url,
			);
		}
		
		echo '<script type="application/ld+json">' . wp_json_encode( $structured_data ) . '</script>' . "\n";
	}
}
add_action( 'wp_head', 'pulsepress_structured_data' );

/**
 * Get custom logo URL.
 *
 * @return string Logo URL or empty string if no logo is set.
 */
function get_custom_logo_url() {
	$custom_logo_id = get_theme_mod( 'custom_logo' );
	$logo_url = '';
	
	if ( $custom_logo_id ) {
		$logo_url = wp_get_attachment_image_url( $custom_logo_id, 'full' );
	}
	
	return $logo_url ? $logo_url : '';
}
