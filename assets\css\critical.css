/**
 * Critical CSS for PulsePress Theme
 * Above-the-fold styles for fast initial render
 */

/* Reset and base styles */
*,
*::before,
*::after {
    box-sizing: border-box;
}

html {
    line-height: 1.15;
    -webkit-text-size-adjust: 100%;
}

body {
    margin: 0;
    font-family: 'Roboto', -apple-system, BlinkMacSystemFont, 'Segoe UI', sans-serif;
    font-size: 16px;
    line-height: 1.6;
    color: #333;
    background-color: #fff;
}

/* Header styles */
.site-header {
    background: #fff;
    box-shadow: 0 2px 4px rgba(0,0,0,0.1);
    position: sticky;
    top: 0;
    z-index: 1000;
}

.header-container {
    max-width: 1200px;
    margin: 0 auto;
    padding: 0 20px;
    display: flex;
    align-items: center;
    justify-content: space-between;
    min-height: 70px;
}

.site-logo {
    font-size: 24px;
    font-weight: 700;
    color: #1e73be;
    text-decoration: none;
}

.site-logo:hover {
    color: #0f5a9e;
}

/* Navigation styles */
.main-navigation {
    display: flex;
    align-items: center;
}

.nav-menu {
    display: flex;
    list-style: none;
    margin: 0;
    padding: 0;
    gap: 30px;
}

.nav-menu a {
    color: #333;
    text-decoration: none;
    font-weight: 500;
    padding: 10px 0;
    transition: color 0.3s ease;
}

.nav-menu a:hover,
.nav-menu a:focus {
    color: #1e73be;
}

/* Mobile menu toggle */
.menu-toggle {
    display: none;
    background: none;
    border: none;
    font-size: 18px;
    cursor: pointer;
    padding: 10px;
}

/* Hero section */
.hero-section {
    background: linear-gradient(135deg, #1e73be 0%, #0f5a9e 100%);
    color: white;
    padding: 80px 0;
    text-align: center;
}

.hero-container {
    max-width: 1200px;
    margin: 0 auto;
    padding: 0 20px;
}

.hero-title {
    font-size: 48px;
    font-weight: 700;
    margin: 0 0 20px 0;
    line-height: 1.2;
}

.hero-subtitle {
    font-size: 20px;
    margin: 0 0 30px 0;
    opacity: 0.9;
}

.hero-cta {
    display: inline-block;
    background: #ff6b6b;
    color: white;
    padding: 15px 30px;
    text-decoration: none;
    border-radius: 5px;
    font-weight: 500;
    transition: background-color 0.3s ease;
}

.hero-cta:hover {
    background: #e55555;
}

/* Main content area */
.site-main {
    max-width: 1200px;
    margin: 0 auto;
    padding: 40px 20px;
}

/* Post grid */
.posts-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
    gap: 30px;
    margin-top: 40px;
}

.post-card {
    background: #fff;
    border-radius: 8px;
    box-shadow: 0 4px 6px rgba(0,0,0,0.1);
    overflow: hidden;
    transition: transform 0.3s ease, box-shadow 0.3s ease;
}

.post-card:hover {
    transform: translateY(-5px);
    box-shadow: 0 8px 15px rgba(0,0,0,0.15);
}

.post-thumbnail {
    width: 100%;
    height: 200px;
    object-fit: cover;
}

.post-content {
    padding: 20px;
}

.post-title {
    font-size: 20px;
    font-weight: 600;
    margin: 0 0 10px 0;
    line-height: 1.3;
}

.post-title a {
    color: #333;
    text-decoration: none;
}

.post-title a:hover {
    color: #1e73be;
}

.post-excerpt {
    color: #666;
    margin: 0 0 15px 0;
    line-height: 1.5;
}

.post-meta {
    font-size: 14px;
    color: #999;
    display: flex;
    gap: 15px;
}

/* Utility classes */
.sr-only {
    position: absolute;
    width: 1px;
    height: 1px;
    padding: 0;
    margin: -1px;
    overflow: hidden;
    clip: rect(0,0,0,0);
    white-space: nowrap;
    border: 0;
}

/* Loading animation */
.loading {
    display: inline-block;
    width: 20px;
    height: 20px;
    border: 3px solid rgba(255,255,255,.3);
    border-radius: 50%;
    border-top-color: #fff;
    animation: spin 1s ease-in-out infinite;
}

@keyframes spin {
    to { transform: rotate(360deg); }
}

/* Responsive design */
@media (max-width: 768px) {
    .menu-toggle {
        display: block;
    }
    
    .nav-menu {
        display: none;
        position: absolute;
        top: 100%;
        left: 0;
        right: 0;
        background: #fff;
        flex-direction: column;
        box-shadow: 0 2px 4px rgba(0,0,0,0.1);
        padding: 20px;
        gap: 0;
    }
    
    .nav-menu.active {
        display: flex;
    }
    
    .nav-menu li {
        border-bottom: 1px solid #eee;
    }
    
    .nav-menu li:last-child {
        border-bottom: none;
    }
    
    .nav-menu a {
        display: block;
        padding: 15px 0;
    }
    
    .hero-title {
        font-size: 32px;
    }
    
    .hero-subtitle {
        font-size: 18px;
    }
    
    .posts-grid {
        grid-template-columns: 1fr;
        gap: 20px;
    }
    
    .site-main {
        padding: 20px 15px;
    }
}

@media (max-width: 480px) {
    .header-container {
        padding: 0 15px;
        min-height: 60px;
    }
    
    .site-logo {
        font-size: 20px;
    }
    
    .hero-section {
        padding: 60px 0;
    }
    
    .hero-title {
        font-size: 28px;
    }
    
    .hero-subtitle {
        font-size: 16px;
    }
    
    .hero-cta {
        padding: 12px 24px;
        font-size: 14px;
    }
}
